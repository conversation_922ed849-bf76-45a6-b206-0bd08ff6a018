import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:dealbeacon/models/promotion.dart';
import 'package:dealbeacon/models/user_model.dart';
import 'package:dealbeacon/models/store.dart';
import 'package:dealbeacon/services/firebase_client.dart';

class FirestoreService {
  static final FirebaseFirestore _db = FirebaseClient.firestore;

  // Collections
  static const String _promotionsCollection = 'promotions';
  static const String _usersCollection = 'users';
  static const String _storesCollection = 'stores';
  static const String _submissionsCollection = 'user_submissions';

  // User operations
  Future<void> createUser(UserModel user) async {
    await _db.collection(_usersCollection).doc(user.uid).set(user.toFirestore());
  }

  Future<UserModel?> getUser(String uid) async {
    final doc = await _db.collection(_usersCollection).doc(uid).get();
    return doc.exists ? UserModel.fromFirestore(doc) : null;
  }

  Future<bool> userExists(String uid) async {
    final doc = await _db.collection(_usersCollection).doc(uid).get();
    return doc.exists;
  }

  Future<void> updateUser(UserModel user) async {
    await _db.collection(_usersCollection).doc(user.uid).update(user.toFirestore());
  }

  Future<void> updateUserLastLogin(String uid) async {
    await _db.collection(_usersCollection).doc(uid).update({
      'lastLoginAt': FieldValue.serverTimestamp(),
    });
  }

  Future<void> deleteUser(String uid) async {
    await _db.collection(_usersCollection).doc(uid).delete();
  }

  Future<void> addFavoritePromotion(String uid, String promotionId) async {
    await _db.collection(_usersCollection).doc(uid).update({
      'favoritePromotionIds': FieldValue.arrayUnion([promotionId])
    });
  }

  Future<void> removeFavoritePromotion(String uid, String promotionId) async {
    await _db.collection(_usersCollection).doc(uid).update({
      'favoritePromotionIds': FieldValue.arrayRemove([promotionId])
    });
  }

  Future<void> addFavoriteStore(String uid, String storeId) async {
    await _db.collection(_usersCollection).doc(uid).update({
      'favoriteStoreIds': FieldValue.arrayUnion([storeId])
    });
  }

  Future<void> removeFavoriteStore(String uid, String storeId) async {
    await _db.collection(_usersCollection).doc(uid).update({
      'favoriteStoreIds': FieldValue.arrayRemove([storeId])
    });
  }

  // Promotion operations
  Stream<List<Promotion>> getPromotions({
    int limit = 20,
    String? category,
    String? country,
    String? storeId,
  }) {
    Query query = _db.collection(_promotionsCollection)
        .where('isActive', isEqualTo: true)
        .orderBy('createdAt', descending: true)
        .limit(limit);

    if (category != null && category.isNotEmpty) {
      query = query.where('category', isEqualTo: category);
    }
    
    if (country != null && country.isNotEmpty) {
      query = query.where('country', isEqualTo: country);
    }
    
    if (storeId != null && storeId.isNotEmpty) {
      query = query.where('storeId', isEqualTo: storeId);
    }

    return query.snapshots().map((snapshot) =>
        snapshot.docs.map((doc) => Promotion.fromFirestore(doc)).toList());
  }

  Stream<List<Promotion>> getFeaturedPromotions({int limit = 10}) {
    return _db
        .collection(_promotionsCollection)
        .where('isActive', isEqualTo: true)
        .where('isFeatured', isEqualTo: true)
        .orderBy('createdAt', descending: true)
        .limit(limit)
        .snapshots()
        .map((snapshot) =>
            snapshot.docs.map((doc) => Promotion.fromFirestore(doc)).toList());
  }

  Stream<List<Promotion>> getFlashDeals({int limit = 10}) {
    return _db
        .collection(_promotionsCollection)
        .where('isActive', isEqualTo: true)
        .where('isFlashDeal', isEqualTo: true)
        .orderBy('validTo')
        .limit(limit)
        .snapshots()
        .map((snapshot) =>
            snapshot.docs.map((doc) => Promotion.fromFirestore(doc)).toList());
  }

  Future<List<Promotion>> searchPromotions(String searchTerm, {int limit = 20}) async {
    // Search in product names and tags
    final queries = [
      _db
          .collection(_promotionsCollection)
          .where('isActive', isEqualTo: true)
          .where('productName', isGreaterThanOrEqualTo: searchTerm)
          .where('productName', isLessThanOrEqualTo: searchTerm + '\uf8ff')
          .limit(limit),
      _db
          .collection(_promotionsCollection)
          .where('isActive', isEqualTo: true)
          .where('tags', arrayContains: searchTerm.toLowerCase())
          .limit(limit),
    ];

    final results = await Future.wait(queries.map((q) => q.get()));
    final Set<String> seenIds = {};
    final List<Promotion> promotions = [];

    for (final snapshot in results) {
      for (final doc in snapshot.docs) {
        if (!seenIds.contains(doc.id)) {
          seenIds.add(doc.id);
          promotions.add(Promotion.fromFirestore(doc));
        }
      }
    }

    return promotions;
  }

  Future<Promotion?> getPromotion(String id) async {
    final doc = await _db.collection(_promotionsCollection).doc(id).get();
    return doc.exists ? Promotion.fromFirestore(doc) : null;
  }

  Future<void> incrementPromotionView(String promotionId) async {
    await _db.collection(_promotionsCollection).doc(promotionId).update({
      'viewCount': FieldValue.increment(1),
    });
  }

  Future<void> incrementPromotionSave(String promotionId) async {
    await _db.collection(_promotionsCollection).doc(promotionId).update({
      'saveCount': FieldValue.increment(1),
    });
  }

  Future<void> incrementPromotionShare(String promotionId) async {
    await _db.collection(_promotionsCollection).doc(promotionId).update({
      'shareCount': FieldValue.increment(1),
    });
  }

  // Store operations
  Stream<List<Store>> getStores({int limit = 20}) {
    return _db
        .collection(_storesCollection)
        .where('isActive', isEqualTo: true)
        .orderBy('followerCount', descending: true)
        .limit(limit)
        .snapshots()
        .map((snapshot) =>
            snapshot.docs.map((doc) => Store.fromFirestore(doc)).toList());
  }

  Future<Store?> getStore(String id) async {
    final doc = await _db.collection(_storesCollection).doc(id).get();
    return doc.exists ? Store.fromFirestore(doc) : null;
  }

  Stream<List<Promotion>> getStorePromotions(String storeId, {int limit = 10}) {
    return _db
        .collection(_promotionsCollection)
        .where('storeId', isEqualTo: storeId)
        .where('isActive', isEqualTo: true)
        .orderBy('createdAt', descending: true)
        .limit(limit)
        .snapshots()
        .map((snapshot) =>
            snapshot.docs.map((doc) => Promotion.fromFirestore(doc)).toList());
  }

  // Get favorite promotions for a user
  Stream<List<Promotion>> getFavoritePromotions(String uid) {
    return getUser(uid).asStream().asyncExpand((user) {
      if (user == null || user.favoritePromotionIds.isEmpty) {
        return Stream.value(<Promotion>[]);
      }
      
      return _db
          .collection(_promotionsCollection)
          .where(FieldPath.documentId, whereIn: user.favoritePromotionIds)
          .snapshots()
          .map((snapshot) =>
              snapshot.docs.map((doc) => Promotion.fromFirestore(doc)).toList());
    });
  }

  // Categories
  List<String> get categories => [
    'Electronics',
    'Fashion & Clothing',
    'Grocery & Food',
    'Home & Garden',
    'Health & Beauty',
    'Sports & Outdoor',
    'Books & Media',
    'Travel & Hotels',
    'Automotive',
    'Toys & Games',
  ];

  // User submission operations
  Future<void> createUserSubmission(Map<String, dynamic> submissionData) async {
    await _db.collection(_submissionsCollection).add({
      ...submissionData,
      'submittedAt': FieldValue.serverTimestamp(),
      'status': 'pending',
    });
  }

  // Batch operations for better performance
  Future<void> batchUpdatePromotions(List<Map<String, dynamic>> updates) async {
    final batch = _db.batch();
    
    for (final update in updates) {
      final docRef = _db.collection(_promotionsCollection).doc(update['id']);
      batch.update(docRef, update['data']);
    }
    
    await batch.commit();
  }

  // Analytics
  Future<Map<String, int>> getPromotionStats() async {
    final snapshot = await _db.collection(_promotionsCollection).get();
    final promotions = snapshot.docs.map((doc) => Promotion.fromFirestore(doc)).toList();
    
    return {
      'total': promotions.length,
      'active': promotions.where((p) => p.isActive).length,
      'featured': promotions.where((p) => p.isFeatured).length,
      'flashDeals': promotions.where((p) => p.isFlashDeal).length,
    };
  }
}