import 'package:cloud_firestore/cloud_firestore.dart';

class Promotion {
  final String id;
  final String storeId;
  final String storeName;
  final String storeLogoUrl;
  final String productName;
  final String productImageUrl;
  final double originalPrice;
  final double discountedPrice;
  final double discountPercentage;
  final String currency;
  final String category;
  final String description;
  final DateTime validFrom;
  final DateTime validTo;
  final String country;
  final String city;
  final bool isActive;
  final bool isFeatured;
  final int viewCount;
  final int saveCount;
  final int shareCount;
  final DateTime createdAt;
  final DateTime updatedAt;
  final List<String> tags;
  final String sourceUrl;
  final bool isFlashDeal;

  Promotion({
    required this.id,
    required this.storeId,
    required this.storeName,
    required this.storeLogoUrl,
    required this.productName,
    required this.productImageUrl,
    required this.originalPrice,
    required this.discountedPrice,
    required this.discountPercentage,
    required this.currency,
    required this.category,
    required this.description,
    required this.validFrom,
    required this.validTo,
    required this.country,
    required this.city,
    required this.isActive,
    required this.isFeatured,
    required this.viewCount,
    required this.saveCount,
    required this.shareCount,
    required this.createdAt,
    required this.updatedAt,
    required this.tags,
    required this.sourceUrl,
    required this.isFlashDeal,
  });

  factory Promotion.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    return Promotion(
      id: doc.id,
      storeId: data['storeId'] ?? '',
      storeName: data['storeName'] ?? '',
      storeLogoUrl: data['storeLogoUrl'] ?? '',
      productName: data['productName'] ?? '',
      productImageUrl: data['productImageUrl'] ?? '',
      originalPrice: (data['originalPrice'] ?? 0).toDouble(),
      discountedPrice: (data['discountedPrice'] ?? 0).toDouble(),
      discountPercentage: (data['discountPercentage'] ?? 0).toDouble(),
      currency: data['currency'] ?? 'USD',
      category: data['category'] ?? '',
      description: data['description'] ?? '',
      validFrom: (data['validFrom'] as Timestamp).toDate(),
      validTo: (data['validTo'] as Timestamp).toDate(),
      country: data['country'] ?? '',
      city: data['city'] ?? '',
      isActive: data['isActive'] ?? true,
      isFeatured: data['isFeatured'] ?? false,
      viewCount: data['viewCount'] ?? 0,
      saveCount: data['saveCount'] ?? 0,
      shareCount: data['shareCount'] ?? 0,
      createdAt: (data['createdAt'] as Timestamp).toDate(),
      updatedAt: (data['updatedAt'] as Timestamp).toDate(),
      tags: List<String>.from(data['tags'] ?? []),
      sourceUrl: data['sourceUrl'] ?? '',
      isFlashDeal: data['isFlashDeal'] ?? false,
    );
  }

  Map<String, dynamic> toFirestore() => {
        'storeId': storeId,
        'storeName': storeName,
        'storeLogoUrl': storeLogoUrl,
        'productName': productName,
        'productImageUrl': productImageUrl,
        'originalPrice': originalPrice,
        'discountedPrice': discountedPrice,
        'discountPercentage': discountPercentage,
        'currency': currency,
        'category': category,
        'description': description,
        'validFrom': Timestamp.fromDate(validFrom),
        'validTo': Timestamp.fromDate(validTo),
        'country': country,
        'city': city,
        'isActive': isActive,
        'isFeatured': isFeatured,
        'viewCount': viewCount,
        'saveCount': saveCount,
        'shareCount': shareCount,
        'createdAt': Timestamp.fromDate(createdAt),
        'updatedAt': Timestamp.fromDate(updatedAt),
        'tags': tags,
        'sourceUrl': sourceUrl,
        'isFlashDeal': isFlashDeal,
      };

  bool get isValid => DateTime.now().isBefore(validTo) && isActive;

  String get formattedDiscount => '${discountPercentage.toStringAsFixed(0)}%';

  String get formattedOriginalPrice => '$currency ${originalPrice.toStringAsFixed(2)}';

  String get formattedDiscountedPrice => '$currency ${discountedPrice.toStringAsFixed(2)}';

  String get savings => '$currency ${(originalPrice - discountedPrice).toStringAsFixed(2)}';

  Duration get timeRemaining => validTo.difference(DateTime.now());

  bool get isExpiringSoon => timeRemaining.inHours <= 24;

  Promotion copyWith({
    String? id,
    String? storeId,
    String? storeName,
    String? storeLogoUrl,
    String? productName,
    String? productImageUrl,
    double? originalPrice,
    double? discountedPrice,
    double? discountPercentage,
    String? currency,
    String? category,
    String? description,
    DateTime? validFrom,
    DateTime? validTo,
    String? country,
    String? city,
    bool? isActive,
    bool? isFeatured,
    int? viewCount,
    int? saveCount,
    int? shareCount,
    DateTime? createdAt,
    DateTime? updatedAt,
    List<String>? tags,
    String? sourceUrl,
    bool? isFlashDeal,
  }) =>
      Promotion(
        id: id ?? this.id,
        storeId: storeId ?? this.storeId,
        storeName: storeName ?? this.storeName,
        storeLogoUrl: storeLogoUrl ?? this.storeLogoUrl,
        productName: productName ?? this.productName,
        productImageUrl: productImageUrl ?? this.productImageUrl,
        originalPrice: originalPrice ?? this.originalPrice,
        discountedPrice: discountedPrice ?? this.discountedPrice,
        discountPercentage: discountPercentage ?? this.discountPercentage,
        currency: currency ?? this.currency,
        category: category ?? this.category,
        description: description ?? this.description,
        validFrom: validFrom ?? this.validFrom,
        validTo: validTo ?? this.validTo,
        country: country ?? this.country,
        city: city ?? this.city,
        isActive: isActive ?? this.isActive,
        isFeatured: isFeatured ?? this.isFeatured,
        viewCount: viewCount ?? this.viewCount,
        saveCount: saveCount ?? this.saveCount,
        shareCount: shareCount ?? this.shareCount,
        createdAt: createdAt ?? this.createdAt,
        updatedAt: updatedAt ?? this.updatedAt,
        tags: tags ?? this.tags,
        sourceUrl: sourceUrl ?? this.sourceUrl,
        isFlashDeal: isFlashDeal ?? this.isFlashDeal,
      );
}