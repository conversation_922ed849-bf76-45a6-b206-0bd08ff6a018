import 'package:flutter/material.dart';
import 'package:dealbeacon/utils/constants.dart';

class FilterBottomSheet extends StatefulWidget {
  final List<String> selectedCategories;
  final double minDiscount;
  final String sortBy;
  final Function(List<String>) onCategoriesChanged;
  final Function(double) onMinDiscountChanged;
  final Function(String) onSortByChanged;
  final VoidCallback onApply;

  const FilterBottomSheet({
    super.key,
    required this.selectedCategories,
    required this.minDiscount,
    required this.sortBy,
    required this.onCategoriesChanged,
    required this.onMinDiscountChanged,
    required this.onSortByChanged,
    required this.onApply,
  });

  @override
  State<FilterBottomSheet> createState() => _FilterBottomSheetState();
}

class _FilterBottomSheetState extends State<FilterBottomSheet> {
  late List<String> _selectedCategories;
  late double _minDiscount;
  late String _sortBy;

  final List<String> _sortOptions = ['Recent', 'Discount', 'Price', 'Popular'];

  @override
  void initState() {
    super.initState();
    _selectedCategories = List.from(widget.selectedCategories);
    _minDiscount = widget.minDiscount;
    _sortBy = widget.sortBy;
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final size = MediaQuery.of(context).size;

    return Container(
      height: size.height * 0.8,
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(AppConstants.radiusXLarge),
          topRight: Radius.circular(AppConstants.radiusXLarge),
        ),
      ),
      child: Column(
        children: [
          // Handle
          Container(
            margin: const EdgeInsets.only(top: AppConstants.paddingMedium),
            width: 40,
            height: 4,
            decoration: BoxDecoration(
              color: theme.colorScheme.onSurface.withValues(alpha: 0.3),
              borderRadius: BorderRadius.circular(2),
            ),
          ),

          // Header
          Container(
            padding: const EdgeInsets.all(AppConstants.paddingMedium),
            child: Row(
              children: [
                Text(
                  'Filters',
                  style: theme.textTheme.headlineSmall?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                TextButton(
                  onPressed: () {
                    setState(() {
                      _selectedCategories.clear();
                      _minDiscount = 0;
                      _sortBy = 'Recent';
                    });
                  },
                  child: Text(
                    'Clear All',
                    style: TextStyle(
                      color: theme.colorScheme.primary,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
                IconButton(
                  onPressed: () => Navigator.pop(context),
                  icon: Icon(
                    Icons.close,
                    color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
                  ),
                ),
              ],
            ),
          ),

          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.symmetric(horizontal: AppConstants.paddingMedium),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Sort By Section
                  _buildSection(
                    theme,
                    'Sort By',
                    Column(
                      children: _sortOptions.map((option) {
                        return RadioListTile<String>(
                          value: option,
                          groupValue: _sortBy,
                          onChanged: (value) {
                            setState(() => _sortBy = value!);
                          },
                          title: Text(option),
                          activeColor: theme.colorScheme.primary,
                          contentPadding: EdgeInsets.zero,
                        );
                      }).toList(),
                    ),
                  ),

                  const SizedBox(height: AppConstants.paddingLarge),

                  // Categories Section
                  _buildSection(
                    theme,
                    'Categories',
                    Wrap(
                      spacing: AppConstants.paddingSmall,
                      runSpacing: AppConstants.paddingSmall,
                      children: AppConstants.categoryIcons.keys.map((category) {
                        final isSelected = _selectedCategories.contains(category);
                        return FilterChip(
                          selected: isSelected,
                          label: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Icon(
                                AppConstants.categoryIcons[category],
                                size: 16,
                                color: isSelected 
                                    ? theme.colorScheme.onPrimary 
                                    : theme.colorScheme.primary,
                              ),
                              const SizedBox(width: 4),
                              Text(category),
                            ],
                          ),
                          onSelected: (selected) {
                            setState(() {
                              if (selected) {
                                _selectedCategories.add(category);
                              } else {
                                _selectedCategories.remove(category);
                              }
                            });
                          },
                          backgroundColor: theme.colorScheme.surface,
                          selectedColor: theme.colorScheme.primary,
                          checkmarkColor: theme.colorScheme.onPrimary,
                          side: BorderSide(
                            color: isSelected 
                                ? theme.colorScheme.primary 
                                : theme.colorScheme.outline.withValues(alpha: 0.3),
                          ),
                        );
                      }).toList(),
                    ),
                  ),

                  const SizedBox(height: AppConstants.paddingLarge),

                  // Minimum Discount Section
                  _buildSection(
                    theme,
                    'Minimum Discount',
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          '${_minDiscount.toInt()}% or more',
                          style: theme.textTheme.titleMedium?.copyWith(
                            color: theme.colorScheme.primary,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        const SizedBox(height: AppConstants.paddingMedium),
                        SliderTheme(
                          data: SliderTheme.of(context).copyWith(
                            thumbColor: theme.colorScheme.primary,
                            activeTrackColor: theme.colorScheme.primary,
                            inactiveTrackColor: theme.colorScheme.outline.withValues(alpha: 0.3),
                            overlayColor: theme.colorScheme.primary.withValues(alpha: 0.2),
                          ),
                          child: Slider(
                            value: _minDiscount,
                            min: 0,
                            max: 80,
                            divisions: 8,
                            label: '${_minDiscount.toInt()}%',
                            onChanged: (value) {
                              setState(() => _minDiscount = value);
                            },
                          ),
                        ),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text(
                              '0%',
                              style: theme.textTheme.bodySmall?.copyWith(
                                color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
                              ),
                            ),
                            Text(
                              '80%+',
                              style: theme.textTheme.bodySmall?.copyWith(
                                color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),

                  const SizedBox(height: AppConstants.paddingXLarge),
                ],
              ),
            ),
          ),

          // Apply Button
          Container(
            padding: const EdgeInsets.all(AppConstants.paddingMedium),
            child: SafeArea(
              child: Row(
                children: [
                  Expanded(
                    child: OutlinedButton(
                      onPressed: () => Navigator.pop(context),
                      style: OutlinedButton.styleFrom(
                        minimumSize: const Size.fromHeight(50),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(AppConstants.radiusMedium),
                        ),
                      ),
                      child: const Text(
                        'Cancel',
                        style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
                      ),
                    ),
                  ),
                  const SizedBox(width: AppConstants.paddingMedium),
                  Expanded(
                    flex: 2,
                    child: FilledButton(
                      onPressed: () {
                        widget.onCategoriesChanged(_selectedCategories);
                        widget.onMinDiscountChanged(_minDiscount);
                        widget.onSortByChanged(_sortBy);
                        widget.onApply();
                        Navigator.pop(context);
                      },
                      style: FilledButton.styleFrom(
                        minimumSize: const Size.fromHeight(50),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(AppConstants.radiusMedium),
                        ),
                      ),
                      child: const Text(
                        'Apply Filters',
                        style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSection(ThemeData theme, String title, Widget child) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: theme.textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: AppConstants.paddingMedium),
        child,
      ],
    );
  }
}