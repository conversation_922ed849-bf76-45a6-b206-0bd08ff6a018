/// Firestore Data Schema for PromoDetect Application
/// 
/// This file defines the data structure for all Firestore collections
/// and their relationships in the PromoDetect application.

class FirestoreDataSchema {
  // Collection Names
  static const String promotionsCollection = 'promotions';
  static const String usersCollection = 'users';
  static const String storesCollection = 'stores';
  static const String userSubmissionsCollection = 'user_submissions';
  static const String categoriesCollection = 'categories';

  // Document Schemas
  
  /// Promotions Collection
  /// Documents represent individual product promotions from stores
  static const Map<String, String> promotionsSchema = {
    'id': 'String - Auto-generated document ID',
    'storeId': 'String - Reference to store document',
    'storeName': 'String - Store display name',
    'storeLogoUrl': 'String - Store logo image URL',
    'productName': 'String - Product title/name',
    'productImageUrl': 'String - Product image URL',
    'originalPrice': 'Number - Original product price',
    'discountedPrice': 'Number - Discounted product price',
    'discountPercentage': 'Number - Calculated discount percentage',
    'currency': 'String - Currency code (USD, EUR, etc.)',
    'category': 'String - Product category',
    'description': 'String - Promotion description',
    'validFrom': 'TIMESTAMP - Promotion start date',
    'validTo': 'TIMESTAMP - Promotion end date',
    'country': 'String - Country code (US, FR, etc.)',
    'city': 'String - City name',
    'isActive': 'Boolean - Whether promotion is currently active',
    'isFeatured': 'Boolean - Whether promotion is featured',
    'viewCount': 'Number - Number of views',
    'saveCount': 'Number - Number of saves/favorites',
    'shareCount': 'Number - Number of shares',
    'createdAt': 'TIMESTAMP - Creation timestamp',
    'updatedAt': 'TIMESTAMP - Last update timestamp',
    'tags': 'Array<String> - Searchable tags',
    'sourceUrl': 'String - Original source URL',
    'isFlashDeal': 'Boolean - Whether it is a time-limited flash deal'
  };

  /// Users Collection
  /// Documents represent registered user accounts
  static const Map<String, String> usersSchema = {
    'uid': 'String - Firebase Auth UID',
    'email': 'String - User email address',
    'displayName': 'String - User display name',
    'photoURL': 'String - User profile photo URL',
    'favoriteStoreIds': 'Array<String> - List of favorite store IDs',
    'favoritePromotionIds': 'Array<String> - List of favorite promotion IDs',
    'preferredCategories': 'Array<String> - Preferred product categories',
    'country': 'String - User country code',
    'city': 'String - User city',
    'notificationSettings': 'Map - Notification preferences',
    'submissionCount': 'Number - Number of submissions made',
    'isActive': 'Boolean - Account active status',
    'createdAt': 'TIMESTAMP - Account creation date',
    'lastLoginAt': 'TIMESTAMP - Last login timestamp',
    'language': 'String - Preferred language code'
  };

  /// Stores Collection
  /// Documents represent retail stores/merchants
  static const Map<String, String> storesSchema = {
    'id': 'String - Auto-generated document ID',
    'name': 'String - Store name',
    'logoUrl': 'String - Store logo image URL',
    'category': 'String - Store category',
    'description': 'String - Store description',
    'websiteUrl': 'String - Store website URL',
    'country': 'String - Primary country code',
    'cities': 'Array<String> - Cities where store operates',
    'isActive': 'Boolean - Store active status',
    'isPremium': 'Boolean - Whether store has premium features',
    'promotionCount': 'Number - Number of active promotions',
    'followerCount': 'Number - Number of followers',
    'rating': 'Number - Average rating (1-5)',
    'createdAt': 'TIMESTAMP - Store added date',
    'updatedAt': 'TIMESTAMP - Last update timestamp'
  };

  /// User Submissions Collection
  /// Documents represent user-submitted promotions pending review
  static const Map<String, String> userSubmissionsSchema = {
    'id': 'String - Auto-generated document ID',
    'submittedBy': 'String - User UID who submitted',
    'promotionData': 'Map - Extracted promotion data',
    'imageUrl': 'String - Uploaded image URL',
    'status': 'String - Review status (pending, approved, rejected)',
    'reviewedBy': 'String - Admin UID who reviewed',
    'reviewNotes': 'String - Admin review notes',
    'submittedAt': 'TIMESTAMP - Submission date',
    'reviewedAt': 'TIMESTAMP - Review completion date',
    'country': 'String - Submission country',
    'extractedText': 'String - OCR extracted text'
  };

  /// Categories Collection
  /// Documents represent product categories with localization
  static const Map<String, String> categoriesSchema = {
    'id': 'String - Category ID',
    'name': 'Map - Localized category names',
    'icon': 'String - Material icon name',
    'color': 'String - Category color hex code',
    'isActive': 'Boolean - Category active status',
    'promotionCount': 'Number - Number of promotions in category',
    'sortOrder': 'Number - Display sort order'
  };

  // Sample document structures for reference

  /// Sample Promotion Document
  static const Map<String, dynamic> samplePromotion = {
    'storeId': 'store_walmart_001',
    'storeName': 'Walmart',
    'storeLogoUrl': 'https://example.com/walmart-logo.png',
    'productName': 'Samsung Galaxy S24 Ultra',
    'productImageUrl': 'https://example.com/galaxy-s24.jpg',
    'originalPrice': 1299.99,
    'discountedPrice': 999.99,
    'discountPercentage': 23.08,
    'currency': 'USD',
    'category': 'Electronics',
    'description': 'Limited time offer on flagship smartphone',
    'validFrom': 'TIMESTAMP',
    'validTo': 'TIMESTAMP',
    'country': 'US',
    'city': 'New York',
    'isActive': true,
    'isFeatured': false,
    'viewCount': 1247,
    'saveCount': 89,
    'shareCount': 23,
    'createdAt': 'TIMESTAMP',
    'updatedAt': 'TIMESTAMP',
    'tags': ['smartphone', 'samsung', 'electronics', 'mobile'],
    'sourceUrl': 'https://walmart.com/product/galaxy-s24',
    'isFlashDeal': true
  };

  /// Sample User Document
  static const Map<String, dynamic> sampleUser = {
    'uid': 'firebase_auth_uid_123',
    'email': '<EMAIL>',
    'displayName': 'John Doe',
    'photoURL': 'https://example.com/profile.jpg',
    'favoriteStoreIds': ['store_walmart_001', 'store_target_002'],
    'favoritePromotionIds': ['promo_001', 'promo_002'],
    'preferredCategories': ['Electronics', 'Fashion'],
    'country': 'US',
    'city': 'New York',
    'notificationSettings': {
      'newPromotions': true,
      'favoriteStores': true,
      'flashDeals': true,
      'weeklyDigest': false
    },
    'submissionCount': 5,
    'isActive': true,
    'createdAt': 'TIMESTAMP',
    'lastLoginAt': 'TIMESTAMP',
    'language': 'en'
  };
}