import 'package:flutter/material.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:provider/provider.dart';
import 'package:dealbeacon/theme.dart';
import 'package:dealbeacon/screens/auth/auth_wrapper.dart';
import 'package:dealbeacon/services/auth_service.dart';
import 'package:dealbeacon/services/firebase_client.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  
  // Initialize Firebase with enhanced configuration
  await FirebaseClient.initialize();
  
  runApp(const PromoDetectApp());
}

class PromoDetectApp extends StatelessWidget {
  const PromoDetectApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        StreamProvider<User?>(
          create: (context) => AuthService.authStateChanges,
          initialData: null,
        ),
      ],
      child: MaterialApp(
        title: 'PromoDetect',
        debugShowCheckedModeBanner: false,
        theme: lightTheme,
        darkTheme: darkTheme,
        themeMode: ThemeMode.system,
        home: const AuthWrapper(),
      ),
    );
  }
}
