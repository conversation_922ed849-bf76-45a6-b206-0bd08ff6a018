# Firebase Integration Guide for PromoDetect

This document provides comprehensive information about the Firebase client setup and usage in the PromoDetect application.

## 🔥 Firebase Services Integrated

- **Firebase Auth** - User authentication and management
- **Cloud Firestore** - NoSQL database for app data
- **Firebase Core** - Foundation service for Firebase SDK
- **Offline Support** - Automatic caching and offline persistence

## 📁 File Structure

```
lib/
├── services/
│   ├── firebase_client.dart          # Core Firebase configuration and utilities
│   ├── auth_service.dart             # Authentication operations
│   ├── firestore_service.dart        # Firestore CRUD operations
│   ├── data_repository.dart          # Repository pattern implementation
│   └── sample_data_seeder.dart       # Development data seeding
├── firestore/
│   └── firestore_data_schema.dart    # Database schema documentation
├── widgets/
│   └── connection_status_widget.dart # Connection status indicator
└── firebase_options.dart             # Auto-generated Firebase config

Root files:
├── firebase.json                     # Firebase project configuration
├── firestore.rules                  # Firestore security rules
└── firestore.indexes.json           # Firestore composite indexes
```

## 🚀 Quick Start

### 1. Firebase Initialization

The app automatically initializes Firebase on startup:

```dart
// In main.dart
await FirebaseClient.initialize();
```

### 2. Authentication Usage

```dart
// Sign in
final result = await AuthService.signInWithEmail(email, password);

// Register
final result = await AuthService.registerWithEmail(email, password, displayName);

// Current user
final user = AuthService.currentUser;

// Sign out
await AuthService.signOut();
```

### 3. Firestore Operations

#### Using FirestoreService (Direct)
```dart
final firestoreService = FirestoreService();

// Get promotions
final promotions = firestoreService.getPromotions(limit: 20, category: 'Electronics');

// Add to favorites
await firestoreService.addFavoritePromotion(userId, promotionId);
```

#### Using DataRepository (Recommended)
```dart
final repository = DataRepository();

// Get promotions with error handling
final result = await repository.getPromotions(limit: 20);
if (result.isSuccess) {
  final promotions = result.data;
  // Use promotions
} else {
  print('Error: ${result.error}');
}
```

## 📊 Data Schema

### Collections Overview

1. **users** - User profiles and preferences
2. **promotions** - Product promotions from stores
3. **stores** - Retail store information
4. **user_submissions** - User-submitted promotions (pending review)
5. **categories** - Product categories with localization

### Sample Promotion Document

```dart
{
  'storeId': 'store_walmart_001',
  'storeName': 'Walmart',
  'storeLogoUrl': 'https://example.com/logo.png',
  'productName': 'Samsung Galaxy S24 Ultra',
  'productImageUrl': 'https://example.com/product.jpg',
  'originalPrice': 1299.99,
  'discountedPrice': 999.99,
  'discountPercentage': 23.08,
  'currency': 'USD',
  'category': 'Electronics',
  'description': 'Limited time offer...',
  'validFrom': Timestamp,
  'validTo': Timestamp,
  'country': 'US',
  'city': 'New York',
  'isActive': true,
  'isFeatured': false,
  'viewCount': 1247,
  'saveCount': 89,
  'shareCount': 23,
  'createdAt': Timestamp,
  'updatedAt': Timestamp,
  'tags': ['smartphone', 'samsung'],
  'sourceUrl': 'https://walmart.com/...',
  'isFlashDeal': false
}
```

## 🔐 Security Rules

The Firestore security rules allow:
- **Authenticated users**: Full read/write access to all collections
- **Unauthenticated users**: No access

```javascript
// firestore.rules
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    match /{document=**} {
      allow read, write: if request.auth != null;
    }
  }
}
```

## 📈 Indexes

Composite indexes are created for:
- Category + ValidTo (for filtering by category and sorting by expiry)
- Country + CreatedAt (for location-based newest promotions)
- StoreId + ValidTo (for store-specific active promotions)
- SubmittedBy + SubmittedAt (for user submission history)

## 🛠 Development Tools

### Sample Data Seeding

For development and testing:

```dart
import 'package:dealbeacon/services/sample_data_seeder.dart';

// Seed all sample data
await SampleDataSeeder.seedAll();

// Seed specific data
await SampleDataSeeder.seedPromotions();
await SampleDataSeeder.seedStores();
await SampleDataSeeder.seedCategories();

// Clear all data (use with caution!)
await SampleDataSeeder.clearAll();
```

### Connection Status Monitoring

Add the connection status widget to your app:

```dart
import 'package:dealbeacon/widgets/connection_status_widget.dart';

// In your Scaffold
Scaffold(
  body: Column(
    children: [
      const ConnectionStatusWidget(),
      // Your main content
    ],
  ),
)
```

### Firebase Client Utilities

```dart
// Check connection
final isConnected = await FirebaseClient.testConnection();

// Get app info
final appInfo = FirebaseClient.appInfo;

// Manual network control
await FirebaseClient.disableNetwork();
await FirebaseClient.enableNetwork();

// Error handling
final errorMessage = FirebaseClient.getFirebaseErrorMessage(error);
```

## 📱 Offline Support

The Firebase client includes:
- **Automatic caching** of Firestore data
- **Offline persistence** enabled for mobile platforms
- **Connection status monitoring** via streams
- **Graceful error handling** for network failures

### Offline Features
- Read cached data when offline
- Queue write operations for when connection returns
- Visual indicators for offline status
- Automatic retry of failed operations

## 🔄 Repository Pattern

The `DataRepository` class provides:
- **Error wrapping** with `Result<T>` class
- **Consistent error handling** across the app
- **Clean separation** between UI and data layers
- **Easy testing** and mocking capabilities

Example usage:
```dart
final repository = DataRepository();

// All operations return Result<T>
final result = await repository.getUser(userId);

if (result.isSuccess) {
  final user = result.data;
  // Handle success
} else {
  // Handle error
  showSnackBar(result.error);
}
```

## 🎯 Best Practices

1. **Always use Repository pattern** for data operations
2. **Handle errors gracefully** with proper user feedback
3. **Implement loading states** for better UX
4. **Use streams for real-time data** when appropriate
5. **Leverage offline capabilities** for better performance
6. **Monitor connection status** in your UI
7. **Implement proper pagination** for large datasets
8. **Use batch operations** for multiple updates

## 🐛 Troubleshooting

### Common Issues

1. **Firebase not initialized**
   - Ensure `FirebaseClient.initialize()` is called in main.dart

2. **Permission denied errors**
   - Check that user is authenticated
   - Verify Firestore security rules

3. **Network connectivity issues**
   - Use `ConnectionStatusWidget` to monitor status
   - Implement retry mechanisms for critical operations

4. **Data not updating in real-time**
   - Check if using streams vs. one-time reads
   - Verify listeners are properly set up

### Debug Information

Enable debug logging:
```dart
// Firebase will log debug info in debug mode
// Check console for Firebase-related messages
```

Check Firebase app status:
```dart
final appInfo = FirebaseClient.appInfo;
print('Firebase status: $appInfo');
```

## 📚 Additional Resources

- [Firebase Flutter Documentation](https://firebase.flutter.dev/)
- [Firestore Documentation](https://firebase.google.com/docs/firestore)
- [Firebase Auth Documentation](https://firebase.google.com/docs/auth)
- [PromoDetect Data Schema](lib/firestore/firestore_data_schema.dart)

---

*This integration provides a robust, scalable, and offline-capable Firebase setup for the PromoDetect application.*