{"firestore": {"rules": "firestore.rules", "indexes": "firestore.indexes.json"}, "functions": {"source": "functions", "predeploy": ["npm --prefix \"$RESOURCE_DIR\" run lint", "npm --prefix \"$RESOURCE_DIR\" run build"]}, "hosting": {"public": "build/web", "ignore": ["firebase.json", "**/.*", "**/node_modules/**"], "rewrites": [{"source": "**", "destination": "/index.html"}]}, "flutter": {"platforms": {"dart": {"lib/firebase_options.dart": {"projectId": "h0my1x2u1l7g7ph11m7vqb9xreq96e", "configurations": {"web": "1:288642987663:web:6b6dec01a5988e7cdd22fa"}}}}}}