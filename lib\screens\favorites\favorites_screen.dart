import 'package:flutter/material.dart';
import 'package:dealbeacon/widgets/promotion_card.dart';
import 'package:dealbeacon/models/promotion.dart';
import 'package:dealbeacon/utils/constants.dart';

class FavoritesScreen extends StatefulWidget {
  const FavoritesScreen({super.key});

  @override
  State<FavoritesScreen> createState() => _FavoritesScreenState();
}

class _FavoritesScreenState extends State<FavoritesScreen> with TickerProviderStateMixin {
  final Set<String> _favoritePromotions = {'promo_001', 'promo_003', 'promo_006'}; // Sample favorites
  late TabController _tabController;

  List<Promotion> get _samplePromotions {
    return SampleData.promotions.map((data) {
      return Promotion(
        id: data['id'],
        storeId: data['storeId'],
        storeName: data['storeName'],
        storeLogoUrl: data['storeLogoUrl'],
        productName: data['productName'],
        productImageUrl: data['productImageUrl'],
        originalPrice: data['originalPrice'].toDouble(),
        discountedPrice: data['discountedPrice'].toDouble(),
        discountPercentage: data['discountPercentage'].toDouble(),
        currency: data['currency'],
        category: data['category'],
        description: data['description'],
        validFrom: data['validFrom'],
        validTo: data['validTo'],
        country: data['country'],
        city: data['city'],
        isActive: data['isActive'],
        isFeatured: data['isFeatured'],
        viewCount: data['viewCount'],
        saveCount: data['saveCount'],
        shareCount: data['shareCount'],
        createdAt: data['createdAt'],
        updatedAt: data['updatedAt'],
        tags: List<String>.from(data['tags']),
        sourceUrl: data['sourceUrl'],
        isFlashDeal: data['isFlashDeal'],
      );
    }).toList();
  }

  List<Promotion> get _favoritePromotionsList {
    return _samplePromotions.where((p) => _favoritePromotions.contains(p.id)).toList();
  }

  List<String> get _favoriteStores {
    return ['Samsung', 'Apple', 'Nike', 'Best Buy']; // Sample favorite stores
  }

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  void _toggleFavorite(String promotionId) {
    setState(() {
      if (_favoritePromotions.contains(promotionId)) {
        _favoritePromotions.remove(promotionId);
      } else {
        _favoritePromotions.add(promotionId);
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      backgroundColor: theme.colorScheme.surface,
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        elevation: 0,
        title: Text(
          'Favorites',
          style: theme.textTheme.headlineSmall?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        bottom: TabBar(
          controller: _tabController,
          labelColor: theme.colorScheme.primary,
          unselectedLabelColor: theme.colorScheme.onSurface.withValues(alpha: 0.6),
          indicatorColor: theme.colorScheme.primary,
          labelStyle: const TextStyle(fontWeight: FontWeight.w600),
          tabs: const [
            Tab(text: 'Deals'),
            Tab(text: 'Stores'),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildFavoriteDeals(theme),
          _buildFavoriteStores(theme),
        ],
      ),
    );
  }

  Widget _buildFavoriteDeals(ThemeData theme) {
    if (_favoritePromotionsList.isEmpty) {
      return _buildEmptyState(
        theme,
        Icons.favorite_outline,
        'No Favorite Deals',
        'Start saving deals you love by tapping the heart icon',
        'Browse Deals',
        () {
          // Navigate to home screen
          DefaultTabController.of(context)?.animateTo(0);
        },
      );
    }

    return CustomScrollView(
      slivers: [
        SliverToBoxAdapter(
          child: Padding(
            padding: const EdgeInsets.all(AppConstants.paddingMedium),
            child: Row(
              children: [
                Icon(
                  Icons.favorite,
                  color: theme.colorScheme.error,
                  size: 20,
                ),
                const SizedBox(width: AppConstants.paddingSmall),
                Text(
                  '${_favoritePromotionsList.length} saved deals',
                  style: theme.textTheme.bodyMedium?.copyWith(
                    color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const Spacer(),
                TextButton(
                  onPressed: () {
                    // TODO: Show sort options
                  },
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Text(
                        'Sort',
                        style: TextStyle(
                          color: theme.colorScheme.primary,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      const SizedBox(width: 4),
                      Icon(
                        Icons.sort,
                        size: 16,
                        color: theme.colorScheme.primary,
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
        SliverList(
          delegate: SliverChildBuilderDelegate(
            (context, index) {
              final promotion = _favoritePromotionsList[index];
              return PromotionCard(
                promotion: promotion,
                isFavorited: true,
                onFavorite: () => _toggleFavorite(promotion.id),
                onTap: () {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(content: Text('Opening ${promotion.productName}')),
                  );
                },
              );
            },
            childCount: _favoritePromotionsList.length,
          ),
        ),
        const SliverToBoxAdapter(
          child: SizedBox(height: AppConstants.paddingXLarge),
        ),
      ],
    );
  }

  Widget _buildFavoriteStores(ThemeData theme) {
    if (_favoriteStores.isEmpty) {
      return _buildEmptyState(
        theme,
        Icons.store_outlined,
        'No Favorite Stores',
        'Follow your favorite stores to get notified of their latest deals',
        'Discover Stores',
        () {
          // Navigate to search screen
          DefaultTabController.of(context)?.animateTo(1);
        },
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(AppConstants.paddingMedium),
      itemCount: _favoriteStores.length,
      itemBuilder: (context, index) {
        final storeName = _favoriteStores[index];
        final storeLogoUrl = AppConstants.storeLogos[storeName] ?? '';
        
        return Container(
          margin: const EdgeInsets.only(bottom: AppConstants.paddingMedium),
          decoration: BoxDecoration(
            color: theme.colorScheme.surface,
            borderRadius: BorderRadius.circular(AppConstants.radiusLarge),
            border: Border.all(
              color: theme.colorScheme.outline.withValues(alpha: 0.1),
              width: 1,
            ),
            boxShadow: AppConstants.lightShadow,
          ),
          child: ListTile(
            contentPadding: const EdgeInsets.all(AppConstants.paddingMedium),
            leading: Container(
              width: 48,
              height: 48,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(AppConstants.radiusMedium),
                color: theme.colorScheme.surfaceContainerHighest,
              ),
              child: storeLogoUrl.isNotEmpty
                  ? ClipRRect(
                      borderRadius: BorderRadius.circular(AppConstants.radiusMedium),
                      child: Image.network(
                        storeLogoUrl,
                        fit: BoxFit.contain,
                        errorBuilder: (context, error, stackTrace) => Icon(
                          Icons.store,
                          color: theme.colorScheme.primary,
                        ),
                      ),
                    )
                  : Icon(
                      Icons.store,
                      color: theme.colorScheme.primary,
                    ),
            ),
            title: Text(
              storeName,
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            subtitle: Text(
              'Get notified of new deals',
              style: theme.textTheme.bodyMedium?.copyWith(
                color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
              ),
            ),
            trailing: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                IconButton(
                  onPressed: () {
                    // TODO: Open store notifications settings
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(content: Text('Notification settings coming soon!')),
                    );
                  },
                  icon: Icon(
                    Icons.notifications_outlined,
                    color: theme.colorScheme.primary,
                  ),
                ),
                IconButton(
                  onPressed: () {
                    setState(() {
                      _favoriteStores.remove(storeName);
                    });
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text('Unfollowed $storeName'),
                        action: SnackBarAction(
                          label: 'Undo',
                          onPressed: () {
                            setState(() {
                              _favoriteStores.add(storeName);
                            });
                          },
                        ),
                      ),
                    );
                  },
                  icon: Icon(
                    Icons.favorite,
                    color: theme.colorScheme.error,
                  ),
                ),
              ],
            ),
            onTap: () {
              // TODO: Navigate to store page
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(content: Text('Opening $storeName store page')),
              );
            },
          ),
        );
      },
    );
  }

  Widget _buildEmptyState(
    ThemeData theme,
    IconData icon,
    String title,
    String subtitle,
    String buttonText,
    VoidCallback onButtonPressed,
  ) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.paddingLarge),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              icon,
              size: 80,
              color: theme.colorScheme.onSurface.withValues(alpha: 0.3),
            ),
            const SizedBox(height: AppConstants.paddingLarge),
            Text(
              title,
              style: theme.textTheme.headlineSmall?.copyWith(
                color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: AppConstants.paddingMedium),
            Text(
              subtitle,
              style: theme.textTheme.bodyMedium?.copyWith(
                color: theme.colorScheme.onSurface.withValues(alpha: 0.5),
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: AppConstants.paddingXLarge),
            FilledButton(
              onPressed: onButtonPressed,
              style: FilledButton.styleFrom(
                padding: const EdgeInsets.symmetric(
                  horizontal: AppConstants.paddingLarge,
                  vertical: AppConstants.paddingMedium,
                ),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(AppConstants.radiusMedium),
                ),
              ),
              child: Text(
                buttonText,
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}