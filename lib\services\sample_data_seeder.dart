import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:dealbeacon/services/firebase_client.dart';
import 'package:flutter/foundation.dart';

/// Seeder for sample data during development
class SampleDataSeeder {
  static final FirebaseFirestore _db = FirebaseClient.firestore;

  /// Seed sample promotions data
  static Future<void> seedPromotions() async {
    try {
      final batch = FirebaseClient.batch();
      final now = DateTime.now();

      // Sample promotions data
      final promotions = [
        {
          'storeId': 'store_walmart_001',
          'storeName': 'Walmart',
          'storeLogoUrl': 'https://images.unsplash.com/photo-1441986300917-64674bd600d8?w=100&h=100&fit=crop&crop=center',
          'productName': 'Samsung Galaxy S24 Ultra',
          'productImageUrl': 'https://images.unsplash.com/photo-1511707171634-5f897ff02aa9?w=300&h=300&fit=crop&crop=center',
          'originalPrice': 1299.99,
          'discountedPrice': 999.99,
          'discountPercentage': 23.08,
          'currency': 'USD',
          'category': 'Electronics',
          'description': 'Limited time offer on flagship smartphone with advanced AI features',
          'validFrom': Timestamp.fromDate(now),
          'validTo': Timestamp.fromDate(now.add(const Duration(days: 30))),
          'country': 'US',
          'city': 'New York',
          'isActive': true,
          'isFeatured': true,
          'viewCount': 1247,
          'saveCount': 89,
          'shareCount': 23,
          'createdAt': FirebaseClient.serverTimestamp,
          'updatedAt': FirebaseClient.serverTimestamp,
          'tags': ['smartphone', 'samsung', 'electronics', 'mobile', 'flagship'],
          'sourceUrl': 'https://walmart.com/product/galaxy-s24',
          'isFlashDeal': false,
        },
        {
          'storeId': 'store_target_002',
          'storeName': 'Target',
          'storeLogoUrl': 'https://images.unsplash.com/photo-1560472355-536de3962603?w=100&h=100&fit=crop&crop=center',
          'productName': 'Nike Air Max 270',
          'productImageUrl': 'https://images.unsplash.com/photo-1542291026-7eec264c27ff?w=300&h=300&fit=crop&crop=center',
          'originalPrice': 150.00,
          'discountedPrice': 89.99,
          'discountPercentage': 40.01,
          'currency': 'USD',
          'category': 'Fashion & Clothing',
          'description': 'Flash sale on popular Nike sneakers - limited sizes available',
          'validFrom': Timestamp.fromDate(now),
          'validTo': Timestamp.fromDate(now.add(const Duration(days: 7))),
          'country': 'US',
          'city': 'Los Angeles',
          'isActive': true,
          'isFeatured': false,
          'viewCount': 892,
          'saveCount': 156,
          'shareCount': 45,
          'createdAt': FirebaseClient.serverTimestamp,
          'updatedAt': FirebaseClient.serverTimestamp,
          'tags': ['shoes', 'nike', 'sneakers', 'fashion', 'sports'],
          'sourceUrl': 'https://target.com/product/nike-air-max',
          'isFlashDeal': true,
        },
        {
          'storeId': 'store_bestbuy_003',
          'storeName': 'Best Buy',
          'storeLogoUrl': 'https://images.unsplash.com/photo-1557804506-669a67965ba0?w=100&h=100&fit=crop&crop=center',
          'productName': 'MacBook Air M3',
          'productImageUrl': 'https://images.unsplash.com/photo-1541807084-5c52b6b3adef?w=300&h=300&fit=crop&crop=center',
          'originalPrice': 1199.00,
          'discountedPrice': 999.00,
          'discountPercentage': 16.68,
          'currency': 'USD',
          'category': 'Electronics',
          'description': 'Back to school special on Apple MacBook Air with M3 chip',
          'validFrom': Timestamp.fromDate(now),
          'validTo': Timestamp.fromDate(now.add(const Duration(days: 14))),
          'country': 'US',
          'city': 'Seattle',
          'isActive': true,
          'isFeatured': true,
          'viewCount': 2156,
          'saveCount': 234,
          'shareCount': 67,
          'createdAt': FirebaseClient.serverTimestamp,
          'updatedAt': FirebaseClient.serverTimestamp,
          'tags': ['laptop', 'apple', 'macbook', 'computer', 'students'],
          'sourceUrl': 'https://bestbuy.com/product/macbook-air-m3',
          'isFlashDeal': false,
        },
        {
          'storeId': 'store_amazonfresh_004',
          'storeName': 'Amazon Fresh',
          'storeLogoUrl': 'https://images.unsplash.com/photo-1523474253046-8cd2748b5fd2?w=100&h=100&fit=crop&crop=center',
          'productName': 'Organic Grocery Bundle',
          'productImageUrl': 'https://images.unsplash.com/photo-1542838132-92c53300491e?w=300&h=300&fit=crop&crop=center',
          'originalPrice': 89.99,
          'discountedPrice': 59.99,
          'discountPercentage': 33.33,
          'currency': 'USD',
          'category': 'Grocery & Food',
          'description': 'Weekly organic produce bundle with free delivery',
          'validFrom': Timestamp.fromDate(now),
          'validTo': Timestamp.fromDate(now.add(const Duration(days: 7))),
          'country': 'US',
          'city': 'San Francisco',
          'isActive': true,
          'isFeatured': false,
          'viewCount': 445,
          'saveCount': 78,
          'shareCount': 12,
          'createdAt': FirebaseClient.serverTimestamp,
          'updatedAt': FirebaseClient.serverTimestamp,
          'tags': ['organic', 'groceries', 'healthy', 'fresh', 'vegetables'],
          'sourceUrl': 'https://amazon.com/fresh/organic-bundle',
          'isFlashDeal': false,
        },
        {
          'storeId': 'store_homedepot_005',
          'storeName': 'Home Depot',
          'storeLogoUrl': 'https://images.unsplash.com/photo-**********-3c8c76ca7d13?w=100&h=100&fit=crop&crop=center',
          'productName': 'DeWalt Power Tool Set',
          'productImageUrl': 'https://images.unsplash.com/photo-1572981779307-38b8cabb2407?w=300&h=300&fit=crop&crop=center',
          'originalPrice': 299.99,
          'discountedPrice': 199.99,
          'discountPercentage': 33.33,
          'currency': 'USD',
          'category': 'Home & Garden',
          'description': 'Professional grade power tool set - perfect for home improvement',
          'validFrom': Timestamp.fromDate(now),
          'validTo': Timestamp.fromDate(now.add(const Duration(days: 21))),
          'country': 'US',
          'city': 'Chicago',
          'isActive': true,
          'isFeatured': true,
          'viewCount': 1023,
          'saveCount': 198,
          'shareCount': 34,
          'createdAt': FirebaseClient.serverTimestamp,
          'updatedAt': FirebaseClient.serverTimestamp,
          'tags': ['tools', 'dewalt', 'power tools', 'diy', 'construction'],
          'sourceUrl': 'https://homedepot.com/dewalt-tool-set',
          'isFlashDeal': false,
        },
      ];

      for (int i = 0; i < promotions.length; i++) {
        final docRef = _db.collection('promotions').doc();
        batch.set(docRef, promotions[i]);
      }

      await batch.commit();
      if (kDebugMode) {
        print('Successfully seeded ${promotions.length} promotions');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error seeding promotions: $e');
      }
    }
  }

  /// Seed sample stores data
  static Future<void> seedStores() async {
    try {
      final batch = FirebaseClient.batch();

      final stores = [
        {
          'id': 'store_walmart_001',
          'name': 'Walmart',
          'logoUrl': 'https://images.unsplash.com/photo-1441986300917-64674bd600d8?w=100&h=100&fit=crop&crop=center',
          'category': 'Department Store',
          'description': 'Save money. Live better. Americas largest retailer.',
          'websiteUrl': 'https://walmart.com',
          'country': 'US',
          'cities': ['New York', 'Los Angeles', 'Chicago', 'Houston', 'Phoenix'],
          'isActive': true,
          'isPremium': true,
          'promotionCount': 1247,
          'followerCount': 156789,
          'rating': 4.2,
          'createdAt': FirebaseClient.serverTimestamp,
          'updatedAt': FirebaseClient.serverTimestamp,
        },
        {
          'id': 'store_target_002',
          'name': 'Target',
          'logoUrl': 'https://images.unsplash.com/photo-1560472355-536de3962603?w=100&h=100&fit=crop&crop=center',
          'category': 'Department Store',
          'description': 'Expect more. Pay less. Affordable style and quality.',
          'websiteUrl': 'https://target.com',
          'country': 'US',
          'cities': ['Los Angeles', 'San Francisco', 'Seattle', 'Denver'],
          'isActive': true,
          'isPremium': true,
          'promotionCount': 892,
          'followerCount': 123456,
          'rating': 4.4,
          'createdAt': FirebaseClient.serverTimestamp,
          'updatedAt': FirebaseClient.serverTimestamp,
        },
        {
          'id': 'store_bestbuy_003',
          'name': 'Best Buy',
          'logoUrl': 'https://images.unsplash.com/photo-1557804506-669a67965ba0?w=100&h=100&fit=crop&crop=center',
          'category': 'Electronics',
          'description': 'Tech solutions for life. Expert service and support.',
          'websiteUrl': 'https://bestbuy.com',
          'country': 'US',
          'cities': ['Seattle', 'Portland', 'San Jose', 'Austin'],
          'isActive': true,
          'isPremium': true,
          'promotionCount': 2156,
          'followerCount': 89012,
          'rating': 4.1,
          'createdAt': FirebaseClient.serverTimestamp,
          'updatedAt': FirebaseClient.serverTimestamp,
        },
        {
          'id': 'store_amazonfresh_004',
          'name': 'Amazon Fresh',
          'logoUrl': 'https://images.unsplash.com/photo-1523474253046-8cd2748b5fd2?w=100&h=100&fit=crop&crop=center',
          'category': 'Grocery',
          'description': 'Fresh groceries delivered fast. Quality you can trust.',
          'websiteUrl': 'https://amazon.com/fresh',
          'country': 'US',
          'cities': ['San Francisco', 'Oakland', 'San Jose', 'Sacramento'],
          'isActive': true,
          'isPremium': false,
          'promotionCount': 445,
          'followerCount': 67890,
          'rating': 4.0,
          'createdAt': FirebaseClient.serverTimestamp,
          'updatedAt': FirebaseClient.serverTimestamp,
        },
        {
          'id': 'store_homedepot_005',
          'name': 'Home Depot',
          'logoUrl': 'https://images.unsplash.com/photo-**********-3c8c76ca7d13?w=100&h=100&fit=crop&crop=center',
          'category': 'Home Improvement',
          'description': 'How doers get more done. Tools and materials for every project.',
          'websiteUrl': 'https://homedepot.com',
          'country': 'US',
          'cities': ['Chicago', 'Milwaukee', 'Detroit', 'Indianapolis'],
          'isActive': true,
          'isPremium': true,
          'promotionCount': 1023,
          'followerCount': 145678,
          'rating': 4.3,
          'createdAt': FirebaseClient.serverTimestamp,
          'updatedAt': FirebaseClient.serverTimestamp,
        },
      ];

      for (final store in stores) {
        final docRef = _db.collection('stores').doc(store['id'] as String);
        batch.set(docRef, store);
      }

      await batch.commit();
      if (kDebugMode) {
        print('Successfully seeded ${stores.length} stores');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error seeding stores: $e');
      }
    }
  }

  /// Seed sample categories
  static Future<void> seedCategories() async {
    try {
      final batch = FirebaseClient.batch();

      final categories = [
        {
          'id': 'electronics',
          'name': {'en': 'Electronics', 'es': 'Electrónicos', 'fr': 'Électronique'},
          'icon': 'devices',
          'color': '#2196F3',
          'isActive': true,
          'promotionCount': 1247,
          'sortOrder': 1,
        },
        {
          'id': 'fashion',
          'name': {'en': 'Fashion & Clothing', 'es': 'Moda y Ropa', 'fr': 'Mode et Vêtements'},
          'icon': 'checkroom',
          'color': '#E91E63',
          'isActive': true,
          'promotionCount': 892,
          'sortOrder': 2,
        },
        {
          'id': 'grocery',
          'name': {'en': 'Grocery & Food', 'es': 'Comestibles', 'fr': 'Épicerie et Alimentation'},
          'icon': 'local_grocery_store',
          'color': '#4CAF50',
          'isActive': true,
          'promotionCount': 445,
          'sortOrder': 3,
        },
        {
          'id': 'home',
          'name': {'en': 'Home & Garden', 'es': 'Hogar y Jardín', 'fr': 'Maison et Jardin'},
          'icon': 'home',
          'color': '#FF9800',
          'isActive': true,
          'promotionCount': 1023,
          'sortOrder': 4,
        },
        {
          'id': 'beauty',
          'name': {'en': 'Health & Beauty', 'es': 'Salud y Belleza', 'fr': 'Santé et Beauté'},
          'icon': 'face',
          'color': '#9C27B0',
          'isActive': true,
          'promotionCount': 567,
          'sortOrder': 5,
        },
        {
          'id': 'sports',
          'name': {'en': 'Sports & Outdoor', 'es': 'Deportes y Aire Libre', 'fr': 'Sports et Plein Air'},
          'icon': 'sports_soccer',
          'color': '#FF5722',
          'isActive': true,
          'promotionCount': 234,
          'sortOrder': 6,
        },
      ];

      for (final category in categories) {
        final docRef = _db.collection('categories').doc(category['id'] as String);
        batch.set(docRef, category);
      }

      await batch.commit();
      if (kDebugMode) {
        print('Successfully seeded ${categories.length} categories');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error seeding categories: $e');
      }
    }
  }

  /// Seed all sample data
  static Future<void> seedAll() async {
    if (kDebugMode) {
      print('Starting to seed sample data...');
    }
    
    await seedCategories();
    await seedStores();
    await seedPromotions();
    
    if (kDebugMode) {
      print('Sample data seeding completed');
    }
  }

  /// Clear all data (use with caution!)
  static Future<void> clearAll() async {
    if (kDebugMode) {
      print('Clearing all sample data...');
    }

    final batch = FirebaseClient.batch();
    
    // Get all documents from each collection and delete them
    final collections = ['promotions', 'stores', 'categories'];
    
    for (final collection in collections) {
      final snapshot = await _db.collection(collection).get();
      for (final doc in snapshot.docs) {
        batch.delete(doc.reference);
      }
    }
    
    await batch.commit();
    
    if (kDebugMode) {
      print('All sample data cleared');
    }
  }
}