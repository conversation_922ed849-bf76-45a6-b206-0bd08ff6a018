import 'package:cloud_firestore/cloud_firestore.dart';

class Store {
  final String id;
  final String name;
  final String logoUrl;
  final String category;
  final String description;
  final String websiteUrl;
  final String country;
  final List<String> cities;
  final bool isActive;
  final bool isPremium;
  final int promotionCount;
  final int followerCount;
  final double rating;
  final DateTime createdAt;
  final DateTime updatedAt;

  Store({
    required this.id,
    required this.name,
    required this.logoUrl,
    required this.category,
    required this.description,
    required this.websiteUrl,
    required this.country,
    required this.cities,
    required this.isActive,
    required this.isPremium,
    required this.promotionCount,
    required this.followerCount,
    required this.rating,
    required this.createdAt,
    required this.updatedAt,
  });

  factory Store.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    return Store(
      id: doc.id,
      name: data['name'] ?? '',
      logoUrl: data['logoUrl'] ?? '',
      category: data['category'] ?? '',
      description: data['description'] ?? '',
      websiteUrl: data['websiteUrl'] ?? '',
      country: data['country'] ?? '',
      cities: List<String>.from(data['cities'] ?? []),
      isActive: data['isActive'] ?? true,
      isPremium: data['isPremium'] ?? false,
      promotionCount: data['promotionCount'] ?? 0,
      followerCount: data['followerCount'] ?? 0,
      rating: (data['rating'] ?? 0).toDouble(),
      createdAt: (data['createdAt'] as Timestamp).toDate(),
      updatedAt: (data['updatedAt'] as Timestamp).toDate(),
    );
  }

  Map<String, dynamic> toFirestore() => {
        'name': name,
        'logoUrl': logoUrl,
        'category': category,
        'description': description,
        'websiteUrl': websiteUrl,
        'country': country,
        'cities': cities,
        'isActive': isActive,
        'isPremium': isPremium,
        'promotionCount': promotionCount,
        'followerCount': followerCount,
        'rating': rating,
        'createdAt': Timestamp.fromDate(createdAt),
        'updatedAt': Timestamp.fromDate(updatedAt),
      };

  String get formattedRating => rating.toStringAsFixed(1);

  bool get hasActivePromotions => promotionCount > 0;

  Store copyWith({
    String? id,
    String? name,
    String? logoUrl,
    String? category,
    String? description,
    String? websiteUrl,
    String? country,
    List<String>? cities,
    bool? isActive,
    bool? isPremium,
    int? promotionCount,
    int? followerCount,
    double? rating,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) =>
      Store(
        id: id ?? this.id,
        name: name ?? this.name,
        logoUrl: logoUrl ?? this.logoUrl,
        category: category ?? this.category,
        description: description ?? this.description,
        websiteUrl: websiteUrl ?? this.websiteUrl,
        country: country ?? this.country,
        cities: cities ?? this.cities,
        isActive: isActive ?? this.isActive,
        isPremium: isPremium ?? this.isPremium,
        promotionCount: promotionCount ?? this.promotionCount,
        followerCount: followerCount ?? this.followerCount,
        rating: rating ?? this.rating,
        createdAt: createdAt ?? this.createdAt,
        updatedAt: updatedAt ?? this.updatedAt,
      );
}