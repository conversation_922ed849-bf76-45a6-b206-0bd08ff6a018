import 'package:flutter/material.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:provider/provider.dart';
import 'package:dealbeacon/screens/auth/login_screen.dart';
import 'package:dealbeacon/screens/main_screen.dart';

class AuthWrapper extends StatelessWidget {
  const AuthWrapper({super.key});

  @override
  Widget build(BuildContext context) {
    final user = Provider.of<User?>(context);
    
    // Return either the Home or Authentication widget
    if (user == null) {
      return const LoginScreen();
    } else {
      return const MainScreen();
    }
  }
}