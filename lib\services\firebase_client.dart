import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart';
import 'package:dealbeacon/firebase_options.dart';

/// Firebase client configuration and management
class FirebaseClient {
  static bool _initialized = false;
  static FirebaseApp? _app;

  /// Initialize Firebase with proper configuration
  static Future<void> initialize() async {
    if (_initialized) return;

    try {
      _app = await Firebase.initializeApp(
        options: DefaultFirebaseOptions.currentPlatform,
      );

      // Configure Firestore settings with offline persistence
      FirebaseFirestore.instance.settings = const Settings(
        persistenceEnabled: true,
        cacheSizeBytes: Settings.CACHE_SIZE_UNLIMITED,
      );

      // Enable Firebase Auth persistence
      await FirebaseAuth.instance.setPersistence(Persistence.LOCAL);

      _initialized = true;
      
      if (kDebugMode) {
        print('Firebase initialized successfully');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Firebase initialization failed: $e');
      }
      rethrow;
    }
  }

  /// Get Firebase app instance
  static FirebaseApp get app {
    if (!_initialized || _app == null) {
      throw Exception('Firebase not initialized. Call initialize() first.');
    }
    return _app!;
  }

  /// Get Firestore instance with proper configuration
  static FirebaseFirestore get firestore {
    return FirebaseFirestore.instanceFor(app: app);
  }

  /// Get Auth instance
  static FirebaseAuth get auth {
    return FirebaseAuth.instanceFor(app: app);
  }

  /// Check if Firebase is initialized
  static bool get isInitialized => _initialized;

  /// Configure Firestore for development/production
  static Future<void> configureForEnvironment({bool isDevelopment = false}) async {
    final firestore = FirebaseClient.firestore;
    
    if (isDevelopment) {
      // Use emulator for development
      try {
        firestore.useFirestoreEmulator('localhost', 8080);
        FirebaseAuth.instance.useAuthEmulator('localhost', 9099);
        if (kDebugMode) {
          print('Firebase emulators configured');
        }
      } catch (e) {
        if (kDebugMode) {
          print('Emulator configuration failed: $e');
        }
      }
    }
  }

  /// Monitor connection status
  static Stream<bool> get connectionStatus {
    return FirebaseFirestore.instance
        .doc('connectivity/test')
        .snapshots(includeMetadataChanges: true)
        .map((snapshot) => !snapshot.metadata.isFromCache);
  }

  /// Test Firebase connection
  static Future<bool> testConnection() async {
    try {
      await firestore
          .collection('connectivity-test')
          .limit(1)
          .get(const GetOptions(source: Source.server));
      return true;
    } catch (e) {
      if (kDebugMode) {
        print('Connection test failed: $e');
      }
      return false;
    }
  }

  /// Get current user
  static User? get currentUser => auth.currentUser;

  /// Check if user is authenticated
  static bool get isAuthenticated => currentUser != null;

  /// Listen to auth state changes
  static Stream<User?> get authStateChanges => auth.authStateChanges();

  /// Sign out user
  static Future<void> signOut() async {
    await auth.signOut();
  }

  /// Delete current user account
  static Future<void> deleteCurrentUser() async {
    final user = currentUser;
    if (user != null) {
      await user.delete();
    }
  }

  /// Enable/disable network for Firestore
  static Future<void> enableNetwork() async {
    await firestore.enableNetwork();
  }

  static Future<void> disableNetwork() async {
    await firestore.disableNetwork();
  }

  /// Clear Firestore cache
  static Future<void> clearCache() async {
    await firestore.clearPersistence();
  }

  /// Get app info
  static Map<String, String> get appInfo {
    return {
      'name': app.name,
      'projectId': app.options.projectId,
      'initialized': _initialized.toString(),
      'platform': defaultTargetPlatform.name,
    };
  }

  /// Batch operations helper
  static WriteBatch batch() => firestore.batch();

  /// Transaction helper
  static Future<T> runTransaction<T>(
    TransactionHandler<T> updateFunction, {
    Duration timeout = const Duration(seconds: 30),
    int maxAttempts = 5,
  }) {
    return firestore.runTransaction(
      updateFunction,
      timeout: timeout,
      maxAttempts: maxAttempts,
    );
  }

  /// Collection reference helper
  static CollectionReference<Map<String, dynamic>> collection(String path) {
    return firestore.collection(path);
  }

  /// Document reference helper
  static DocumentReference<Map<String, dynamic>> doc(String path) {
    return firestore.doc(path);
  }

  /// Server timestamp helper
  static FieldValue get serverTimestamp => FieldValue.serverTimestamp();

  /// Array operations helpers
  static FieldValue arrayUnion(List elements) => FieldValue.arrayUnion(elements);
  static FieldValue arrayRemove(List elements) => FieldValue.arrayRemove(elements);

  /// Numeric operations helpers
  static FieldValue increment(num value) => FieldValue.increment(value);

  /// Delete field helper
  static FieldValue get delete => FieldValue.delete();

  /// Error handling helper
  static String getFirebaseErrorMessage(Object error) {
    if (error is FirebaseException) {
      switch (error.code) {
        case 'permission-denied':
          return 'Permission denied. Please check your authentication.';
        case 'unavailable':
          return 'Service temporarily unavailable. Please try again later.';
        case 'deadline-exceeded':
          return 'Request timeout. Please check your connection.';
        case 'not-found':
          return 'Requested data not found.';
        case 'already-exists':
          return 'Data already exists.';
        case 'resource-exhausted':
          return 'Quota exceeded. Please try again later.';
        case 'failed-precondition':
          return 'Operation failed due to invalid state.';
        case 'aborted':
          return 'Operation was aborted. Please retry.';
        case 'out-of-range':
          return 'Value out of valid range.';
        case 'unimplemented':
          return 'Feature not implemented.';
        case 'internal':
          return 'Internal error occurred.';
        case 'data-loss':
          return 'Data loss detected.';
        default:
          return error.message ?? 'Unknown Firebase error occurred.';
      }
    }
    return error.toString();
  }

  /// Dispose resources
  static Future<void> dispose() async {
    if (_initialized && _app != null) {
      // Clean up resources if needed
      _initialized = false;
      _app = null;
    }
  }
}