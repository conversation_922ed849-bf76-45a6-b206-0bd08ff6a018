import 'package:flutter/material.dart';
import 'package:dealbeacon/widgets/promotion_card.dart';
import 'package:dealbeacon/models/promotion.dart';
import 'package:dealbeacon/utils/constants.dart';
import 'package:dealbeacon/widgets/filter_bottom_sheet.dart';

class SearchScreen extends StatefulWidget {
  const SearchScreen({super.key});

  @override
  State<SearchScreen> createState() => _SearchScreenState();
}

class _SearchScreenState extends State<SearchScreen> {
  final TextEditingController _searchController = TextEditingController();
  final Set<String> _favoritePromotions = {};
  List<Promotion> _searchResults = [];
  bool _isSearching = false;
  String _sortBy = 'Recent';
  List<String> _selectedCategories = [];
  double _minDiscount = 0;

  List<Promotion> get _samplePromotions {
    return SampleData.promotions.map((data) {
      return Promotion(
        id: data['id'],
        storeId: data['storeId'],
        storeName: data['storeName'],
        storeLogoUrl: data['storeLogoUrl'],
        productName: data['productName'],
        productImageUrl: data['productImageUrl'],
        originalPrice: data['originalPrice'].toDouble(),
        discountedPrice: data['discountedPrice'].toDouble(),
        discountPercentage: data['discountPercentage'].toDouble(),
        currency: data['currency'],
        category: data['category'],
        description: data['description'],
        validFrom: data['validFrom'],
        validTo: data['validTo'],
        country: data['country'],
        city: data['city'],
        isActive: data['isActive'],
        isFeatured: data['isFeatured'],
        viewCount: data['viewCount'],
        saveCount: data['saveCount'],
        shareCount: data['shareCount'],
        createdAt: data['createdAt'],
        updatedAt: data['updatedAt'],
        tags: List<String>.from(data['tags']),
        sourceUrl: data['sourceUrl'],
        isFlashDeal: data['isFlashDeal'],
      );
    }).toList();
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  void _performSearch(String query) {
    if (query.isEmpty) {
      setState(() {
        _searchResults = [];
        _isSearching = false;
      });
      return;
    }

    setState(() => _isSearching = true);

    // Simulate search delay
    Future.delayed(const Duration(milliseconds: 500), () {
      if (mounted) {
        final results = _samplePromotions.where((promotion) {
          final matchesQuery = promotion.productName.toLowerCase().contains(query.toLowerCase()) ||
              promotion.storeName.toLowerCase().contains(query.toLowerCase()) ||
              promotion.tags.any((tag) => tag.toLowerCase().contains(query.toLowerCase()));
          
          final matchesCategory = _selectedCategories.isEmpty || 
              _selectedCategories.contains(promotion.category);
          
          final matchesDiscount = promotion.discountPercentage >= _minDiscount;
          
          return matchesQuery && matchesCategory && matchesDiscount;
        }).toList();

        // Sort results
        switch (_sortBy) {
          case 'Discount':
            results.sort((a, b) => b.discountPercentage.compareTo(a.discountPercentage));
            break;
          case 'Price':
            results.sort((a, b) => a.discountedPrice.compareTo(b.discountedPrice));
            break;
          case 'Popular':
            results.sort((a, b) => b.viewCount.compareTo(a.viewCount));
            break;
          case 'Recent':
          default:
            results.sort((a, b) => b.createdAt.compareTo(a.createdAt));
            break;
        }

        setState(() {
          _searchResults = results;
          _isSearching = false;
        });
      }
    });
  }

  void _toggleFavorite(String promotionId) {
    setState(() {
      if (_favoritePromotions.contains(promotionId)) {
        _favoritePromotions.remove(promotionId);
      } else {
        _favoritePromotions.add(promotionId);
      }
    });
  }

  void _showFilters() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => FilterBottomSheet(
        selectedCategories: _selectedCategories,
        minDiscount: _minDiscount,
        sortBy: _sortBy,
        onCategoriesChanged: (categories) {
          setState(() => _selectedCategories = categories);
        },
        onMinDiscountChanged: (discount) {
          setState(() => _minDiscount = discount);
        },
        onSortByChanged: (sortBy) {
          setState(() => _sortBy = sortBy);
        },
        onApply: () {
          _performSearch(_searchController.text);
        },
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      backgroundColor: theme.colorScheme.surface,
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        elevation: 0,
        title: Text(
          'Search Deals',
          style: theme.textTheme.headlineSmall?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        actions: [
          IconButton(
            onPressed: _showFilters,
            icon: Stack(
              children: [
                Icon(
                  Icons.tune,
                  color: theme.colorScheme.primary,
                ),
                if (_selectedCategories.isNotEmpty || _minDiscount > 0 || _sortBy != 'Recent')
                  Positioned(
                    right: 0,
                    top: 0,
                    child: Container(
                      width: 8,
                      height: 8,
                      decoration: BoxDecoration(
                        color: theme.colorScheme.error,
                        shape: BoxShape.circle,
                      ),
                    ),
                  ),
              ],
            ),
          ),
          const SizedBox(width: AppConstants.paddingMedium),
        ],
      ),
      body: Column(
        children: [
          // Search Bar
          Container(
            padding: const EdgeInsets.all(AppConstants.paddingMedium),
            child: TextField(
              controller: _searchController,
              onChanged: _performSearch,
              decoration: InputDecoration(
                hintText: 'Search for products, brands, or stores...',
                prefixIcon: Icon(
                  Icons.search,
                  color: theme.colorScheme.primary,
                ),
                suffixIcon: _searchController.text.isNotEmpty
                    ? IconButton(
                        onPressed: () {
                          _searchController.clear();
                          _performSearch('');
                        },
                        icon: Icon(
                          Icons.clear,
                          color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
                        ),
                      )
                    : null,
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(AppConstants.radiusMedium),
                  borderSide: BorderSide(
                    color: theme.colorScheme.outline.withValues(alpha: 0.3),
                  ),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(AppConstants.radiusMedium),
                  borderSide: BorderSide(
                    color: theme.colorScheme.primary,
                    width: 2,
                  ),
                ),
                filled: true,
                fillColor: theme.colorScheme.surface,
              ),
            ),
          ),

          // Search Results
          Expanded(
            child: _isSearching
                ? Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        CircularProgressIndicator(
                          color: theme.colorScheme.primary,
                        ),
                        const SizedBox(height: AppConstants.paddingMedium),
                        Text(
                          'Searching for deals...',
                          style: theme.textTheme.bodyMedium?.copyWith(
                            color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
                          ),
                        ),
                      ],
                    ),
                  )
                : _searchController.text.isEmpty
                    ? _buildEmptySearch(theme)
                    : _searchResults.isEmpty
                        ? _buildNoResults(theme)
                        : _buildResults(theme),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptySearch(ThemeData theme) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.search,
            size: 80,
            color: theme.colorScheme.onSurface.withValues(alpha: 0.3),
          ),
          const SizedBox(height: AppConstants.paddingMedium),
          Text(
            'Search for Amazing Deals',
            style: theme.textTheme.headlineSmall?.copyWith(
              color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: AppConstants.paddingSmall),
          Text(
            'Find products, brands, or stores',
            style: theme.textTheme.bodyMedium?.copyWith(
              color: theme.colorScheme.onSurface.withValues(alpha: 0.5),
            ),
          ),
          const SizedBox(height: AppConstants.paddingXLarge),
          
          // Popular searches
          Column(
            children: [
              Text(
                'Popular searches:',
                style: theme.textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
              const SizedBox(height: AppConstants.paddingMedium),
              Wrap(
                spacing: AppConstants.paddingSmall,
                runSpacing: AppConstants.paddingSmall,
                children: ['iPhone', 'Nike', 'Laptop', 'Coffee', 'Headphones']
                    .map((term) => ActionChip(
                          label: Text(term),
                          onPressed: () {
                            _searchController.text = term;
                            _performSearch(term);
                          },
                          backgroundColor: theme.colorScheme.primaryContainer,
                          labelStyle: TextStyle(
                            color: theme.colorScheme.onPrimaryContainer,
                          ),
                        ))
                    .toList(),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildNoResults(ThemeData theme) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.search_off,
            size: 80,
            color: theme.colorScheme.onSurface.withValues(alpha: 0.3),
          ),
          const SizedBox(height: AppConstants.paddingMedium),
          Text(
            'No Results Found',
            style: theme.textTheme.headlineSmall?.copyWith(
              color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: AppConstants.paddingSmall),
          Text(
            'Try adjusting your search or filters',
            style: theme.textTheme.bodyMedium?.copyWith(
              color: theme.colorScheme.onSurface.withValues(alpha: 0.5),
            ),
          ),
          const SizedBox(height: AppConstants.paddingLarge),
          OutlinedButton(
            onPressed: () {
              setState(() {
                _selectedCategories.clear();
                _minDiscount = 0;
                _sortBy = 'Recent';
              });
              _performSearch(_searchController.text);
            },
            child: const Text('Clear Filters'),
          ),
        ],
      ),
    );
  }

  Widget _buildResults(ThemeData theme) {
    return Column(
      children: [
        // Results Count
        Container(
          padding: const EdgeInsets.symmetric(horizontal: AppConstants.paddingMedium),
          child: Row(
            children: [
              Text(
                '${_searchResults.length} results found',
                style: theme.textTheme.bodyMedium?.copyWith(
                  color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
                ),
              ),
              const Spacer(),
              Text(
                'Sorted by $_sortBy',
                style: theme.textTheme.bodySmall?.copyWith(
                  color: theme.colorScheme.onSurface.withValues(alpha: 0.5),
                ),
              ),
            ],
          ),
        ),
        const SizedBox(height: AppConstants.paddingMedium),

        // Results List
        Expanded(
          child: ListView.builder(
            itemCount: _searchResults.length,
            itemBuilder: (context, index) {
              final promotion = _searchResults[index];
              return PromotionCard(
                promotion: promotion,
                isFavorited: _favoritePromotions.contains(promotion.id),
                onFavorite: () => _toggleFavorite(promotion.id),
                onTap: () {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(content: Text('Opening ${promotion.productName}')),
                  );
                },
              );
            },
          ),
        ),
      ],
    );
  }
}