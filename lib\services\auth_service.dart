import 'package:firebase_auth/firebase_auth.dart';
import 'package:dealbeacon/models/user_model.dart';
import 'package:dealbeacon/services/firestore_service.dart';
import 'package:dealbeacon/services/firebase_client.dart';

class AuthService {
  static final FirebaseAuth _auth = FirebaseClient.auth;
  static final FirestoreService _firestoreService = FirestoreService();

  // Get current user
  static User? get currentUser => _auth.currentUser;

  // Auth state changes stream
  static Stream<User?> get authStateChanges => _auth.authStateChanges();

  // Sign in with email and password
  static Future<UserCredential?> signInWithEmail(String email, String password) async {
    try {
      final userCredential = await _auth.signInWithEmailAndPassword(
        email: email.trim(),
        password: password,
      );
      
      if (userCredential.user != null) {
        await _updateLastLoginTime(userCredential.user!.uid);
      }
      
      return userCredential;
    } on FirebaseAuthException catch (e) {
      throw _handleAuthException(e);
    }
  }

  // Register with email and password
  static Future<UserCredential?> registerWithEmail(
    String email,
    String password,
    String displayName,
  ) async {
    try {
      final userCredential = await _auth.createUserWithEmailAndPassword(
        email: email.trim(),
        password: password,
      );
      
      if (userCredential.user != null) {
        // Update display name
        await userCredential.user!.updateDisplayName(displayName);
        
        // Create user document in Firestore
        final userModel = UserModel.create(
          uid: userCredential.user!.uid,
          email: email.trim(),
          displayName: displayName,
        );
        await _firestoreService.createUser(userModel);
      }
      
      return userCredential;
    } on FirebaseAuthException catch (e) {
      throw _handleAuthException(e);
    }
  }

  // Sign in with Google (placeholder for future implementation)
  static Future<UserCredential?> signInWithGoogle() async {
    throw 'Google sign-in is not yet implemented';
  }

  // Send password reset email
  static Future<void> sendPasswordResetEmail(String email) async {
    try {
      await _auth.sendPasswordResetEmail(email: email.trim());
    } on FirebaseAuthException catch (e) {
      throw _handleAuthException(e);
    }
  }

  // Sign out
  static Future<void> signOut() async {
    try {
      await _auth.signOut();
    } catch (e) {
      throw 'Sign out failed: ${e.toString()}';
    }
  }

  // Delete account
  static Future<void> deleteAccount() async {
    try {
      final user = currentUser;
      if (user != null) {
        // Delete user document from Firestore
        await _firestoreService.deleteUser(user.uid);
        // Delete Firebase Auth account
        await user.delete();
      }
    } catch (e) {
      throw 'Account deletion failed: ${e.toString()}';
    }
  }

  // Update last login time
  static Future<void> _updateLastLoginTime(String uid) async {
    try {
      await _firestoreService.updateUserLastLogin(uid);
    } catch (e) {
      // Log error but don't throw to prevent login failure
      print('Failed to update last login time: $e');
    }
  }

  // Handle Firebase Auth exceptions
  static String _handleAuthException(FirebaseAuthException e) {
    switch (e.code) {
      case 'user-not-found':
        return 'No user found with this email address.';
      case 'wrong-password':
        return 'Incorrect password. Please try again.';
      case 'email-already-in-use':
        return 'An account already exists with this email address.';
      case 'weak-password':
        return 'Password is too weak. Please choose a stronger password.';
      case 'invalid-email':
        return 'Invalid email address format.';
      case 'user-disabled':
        return 'This account has been disabled.';
      case 'too-many-requests':
        return 'Too many attempts. Please try again later.';
      case 'operation-not-allowed':
        return 'This sign-in method is not enabled.';
      case 'network-request-failed':
        return 'Network error. Please check your connection.';
      default:
        return 'Authentication failed: ${e.message}';
    }
  }

  // Check if user is logged in
  static bool get isLoggedIn => currentUser != null;

  // Get current user's UID
  static String? get currentUserUid => currentUser?.uid;

  // Get current user's email
  static String? get currentUserEmail => currentUser?.email;

  // Get current user's display name
  static String? get currentUserDisplayName => currentUser?.displayName;

  // Reload current user
  static Future<void> reloadUser() async {
    await currentUser?.reload();
  }
}