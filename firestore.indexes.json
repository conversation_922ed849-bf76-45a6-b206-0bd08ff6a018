{"indexes": [{"collectionGroup": "promotions", "queryScope": "COLLECTION", "fields": [{"fieldPath": "category", "order": "ASCENDING"}, {"fieldPath": "validTo", "order": "DESCENDING"}]}, {"collectionGroup": "promotions", "queryScope": "COLLECTION", "fields": [{"fieldPath": "country", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "promotions", "queryScope": "COLLECTION", "fields": [{"fieldPath": "storeId", "order": "ASCENDING"}, {"fieldPath": "validTo", "order": "DESCENDING"}]}, {"collectionGroup": "user_submissions", "queryScope": "COLLECTION", "fields": [{"fieldPath": "submittedBy", "order": "ASCENDING"}, {"fieldPath": "submittedAt", "order": "DESCENDING"}]}], "fieldOverrides": []}