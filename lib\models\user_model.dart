import 'package:cloud_firestore/cloud_firestore.dart';

class UserModel {
  final String uid;
  final String email;
  final String displayName;
  final String photoURL;
  final List<String> favoriteStoreIds;
  final List<String> favoritePromotionIds;
  final List<String> preferredCategories;
  final String country;
  final String city;
  final Map<String, bool> notificationSettings;
  final int submissionCount;
  final bool isActive;
  final DateTime createdAt;
  final DateTime lastLoginAt;
  final String language;

  UserModel({
    required this.uid,
    required this.email,
    required this.displayName,
    required this.photoURL,
    required this.favoriteStoreIds,
    required this.favoritePromotionIds,
    required this.preferredCategories,
    required this.country,
    required this.city,
    required this.notificationSettings,
    required this.submissionCount,
    required this.isActive,
    required this.createdAt,
    required this.lastLoginAt,
    required this.language,
  });

  factory UserModel.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    return UserModel(
      uid: doc.id,
      email: data['email'] ?? '',
      displayName: data['displayName'] ?? '',
      photoURL: data['photoURL'] ?? '',
      favoriteStoreIds: List<String>.from(data['favoriteStoreIds'] ?? []),
      favoritePromotionIds: List<String>.from(data['favoritePromotionIds'] ?? []),
      preferredCategories: List<String>.from(data['preferredCategories'] ?? []),
      country: data['country'] ?? '',
      city: data['city'] ?? '',
      notificationSettings: Map<String, bool>.from(data['notificationSettings'] ?? _defaultNotificationSettings),
      submissionCount: data['submissionCount'] ?? 0,
      isActive: data['isActive'] ?? true,
      createdAt: (data['createdAt'] as Timestamp).toDate(),
      lastLoginAt: (data['lastLoginAt'] as Timestamp).toDate(),
      language: data['language'] ?? 'en',
    );
  }

  static const Map<String, bool> _defaultNotificationSettings = {
    'newPromotions': true,
    'favoriteStores': true,
    'flashDeals': true,
    'weeklyDigest': false,
  };

  Map<String, dynamic> toFirestore() => {
        'email': email,
        'displayName': displayName,
        'photoURL': photoURL,
        'favoriteStoreIds': favoriteStoreIds,
        'favoritePromotionIds': favoritePromotionIds,
        'preferredCategories': preferredCategories,
        'country': country,
        'city': city,
        'notificationSettings': notificationSettings,
        'submissionCount': submissionCount,
        'isActive': isActive,
        'createdAt': Timestamp.fromDate(createdAt),
        'lastLoginAt': Timestamp.fromDate(lastLoginAt),
        'language': language,
      };

  factory UserModel.create({
    required String uid,
    required String email,
    String displayName = '',
    String photoURL = '',
    String country = '',
    String city = '',
    String language = 'en',
  }) {
    final now = DateTime.now();
    return UserModel(
      uid: uid,
      email: email,
      displayName: displayName,
      photoURL: photoURL,
      favoriteStoreIds: [],
      favoritePromotionIds: [],
      preferredCategories: [],
      country: country,
      city: city,
      notificationSettings: Map<String, bool>.from(_defaultNotificationSettings),
      submissionCount: 0,
      isActive: true,
      createdAt: now,
      lastLoginAt: now,
      language: language,
    );
  }

  bool isFavoritePromotion(String promotionId) => favoritePromotionIds.contains(promotionId);

  bool isFavoriteStore(String storeId) => favoriteStoreIds.contains(storeId);

  UserModel copyWith({
    String? uid,
    String? email,
    String? displayName,
    String? photoURL,
    List<String>? favoriteStoreIds,
    List<String>? favoritePromotionIds,
    List<String>? preferredCategories,
    String? country,
    String? city,
    Map<String, bool>? notificationSettings,
    int? submissionCount,
    bool? isActive,
    DateTime? createdAt,
    DateTime? lastLoginAt,
    String? language,
  }) =>
      UserModel(
        uid: uid ?? this.uid,
        email: email ?? this.email,
        displayName: displayName ?? this.displayName,
        photoURL: photoURL ?? this.photoURL,
        favoriteStoreIds: favoriteStoreIds ?? this.favoriteStoreIds,
        favoritePromotionIds: favoritePromotionIds ?? this.favoritePromotionIds,
        preferredCategories: preferredCategories ?? this.preferredCategories,
        country: country ?? this.country,
        city: city ?? this.city,
        notificationSettings: notificationSettings ?? this.notificationSettings,
        submissionCount: submissionCount ?? this.submissionCount,
        isActive: isActive ?? this.isActive,
        createdAt: createdAt ?? this.createdAt,
        lastLoginAt: lastLoginAt ?? this.lastLoginAt,
        language: language ?? this.language,
      );
}