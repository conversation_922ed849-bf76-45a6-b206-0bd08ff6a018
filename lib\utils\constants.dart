import 'package:flutter/material.dart';

class AppConstants {
  static const String appName = 'PromoDetect';
  static const String appVersion = '1.0.0';
  
  // Sample images for promotions
  static const List<String> sampleImages = [
    'https://pixabay.com/get/g8c1eb2b70b89cbc3d3c7240567f800356ee277bc5f76777fc4ee421041744136f1be965c88a4f0fcbc98842d8a13204c2d96451488cc449a979f46bda661783d_1280.jpg',
    'https://pixabay.com/get/ga47feec6a8435137f00a641a44edf1485b5d39d8de380c036c79b475ad45cc215fb87ff4cf59fc5201a6d842cfe4ca4a937c659ae7ecfd348e90c7e7038142ba_1280.jpg',
    'https://pixabay.com/get/ge36e90eda26680fe83a32ba392e1e0505eee915711b270e3573539d7fc477bbeac3b2ef5d5669308b75a5b878c4de3947af00d0535624358042349949878dead_1280.jpg',
    'https://pixabay.com/get/g03b2a96de9d38fd942a572d4f5dde4f024ee76a3e693a9f082725af87cb34998bc888c0a57886e236050cc3c9d6d0100a749e31fd03e0236f6d465ada803169b_1280.jpg',
    'https://pixabay.com/get/g488afaaa05a2505b47c24b92b45f37e4fa98805ea77a8d4b083c25928319060b986a994e0777569752f4ac41e1eff07106d04cbb6fa0990dfdd0fffd2ec7a1a1_1280.jpg',
    'https://pixabay.com/get/g5b6da5d13192bfe410da53e35317033b9d8ee09ee1dfac69e0bb2540a906273bd5c1c6d930f3d0271420ffd35823eba11bf63fb2cff40f02be864c88d378db15_1280.jpg',
  ];
  
  // Sample store logos
  static const Map<String, String> storeLogos = {
    'Walmart': 'https://logos-world.net/wp-content/uploads/2020/09/Walmart-Logo.png',
    'Target': 'https://corporate.target.com/getmedia/87a1c26f-3a9e-458d-8e62-2e1b8eaaec5c/Target-Logo.png',
    'Best Buy': 'https://logos-world.net/wp-content/uploads/2020/04/Best-Buy-Logo.png',
    'Nike': 'https://logos-world.net/wp-content/uploads/2020/04/Nike-Logo.png',
    'Apple': 'https://logos-world.net/wp-content/uploads/2020/04/Apple-Logo.png',
    'Samsung': 'https://logos-world.net/wp-content/uploads/2020/04/Samsung-Logo.png',
  };

  // Categories with icons
  static const Map<String, IconData> categoryIcons = {
    'Electronics': Icons.phone_android,
    'Fashion & Clothing': Icons.checkroom,
    'Grocery & Food': Icons.local_grocery_store,
    'Home & Garden': Icons.home,
    'Health & Beauty': Icons.spa,
    'Sports & Outdoor': Icons.sports_soccer,
    'Books & Media': Icons.book,
    'Travel & Hotels': Icons.flight,
    'Automotive': Icons.directions_car,
    'Toys & Games': Icons.toys,
  };

  // Animation durations
  static const Duration shortAnimation = Duration(milliseconds: 200);
  static const Duration mediumAnimation = Duration(milliseconds: 400);
  static const Duration longAnimation = Duration(milliseconds: 600);
  
  // Spacing
  static const double paddingSmall = 8.0;
  static const double paddingMedium = 16.0;
  static const double paddingLarge = 24.0;
  static const double paddingXLarge = 32.0;
  
  // Border radius
  static const double radiusSmall = 8.0;
  static const double radiusMedium = 12.0;
  static const double radiusLarge = 16.0;
  static const double radiusXLarge = 24.0;
  
  // Shadow
  static const List<BoxShadow> lightShadow = [
    BoxShadow(
      color: Color(0x0F000000),
      offset: Offset(0, 2),
      blurRadius: 4,
    ),
  ];
  
  static const List<BoxShadow> mediumShadow = [
    BoxShadow(
      color: Color(0x1A000000),
      offset: Offset(0, 4),
      blurRadius: 8,
    ),
  ];
}

class SampleData {
  // Sample promotions data for testing
  static final List<Map<String, dynamic>> promotions = [
    {
      'id': 'promo_001',
      'storeId': 'store_samsung',
      'storeName': 'Samsung',
      'storeLogoUrl': AppConstants.storeLogos['Samsung'] ?? '',
      'productName': 'Galaxy S24 Ultra 256GB',
      'productImageUrl': AppConstants.sampleImages[0],
      'originalPrice': 1299.99,
      'discountedPrice': 999.99,
      'discountPercentage': 23.08,
      'currency': 'USD',
      'category': 'Electronics',
      'description': 'Latest flagship smartphone with AI camera features',
      'validFrom': DateTime.now().subtract(const Duration(days: 1)),
      'validTo': DateTime.now().add(const Duration(days: 7)),
      'country': 'US',
      'city': 'New York',
      'isActive': true,
      'isFeatured': true,
      'viewCount': 1247,
      'saveCount': 89,
      'shareCount': 23,
      'createdAt': DateTime.now().subtract(const Duration(hours: 12)),
      'updatedAt': DateTime.now(),
      'tags': ['smartphone', 'samsung', 'electronics', 'mobile'],
      'sourceUrl': 'https://example.com/galaxy-s24',
      'isFlashDeal': false,
    },
    {
      'id': 'promo_002',
      'storeId': 'store_nike',
      'storeName': 'Nike',
      'storeLogoUrl': AppConstants.storeLogos['Nike'] ?? '',
      'productName': 'Air Max 270 Running Shoes',
      'productImageUrl': AppConstants.sampleImages[1],
      'originalPrice': 150.00,
      'discountedPrice': 89.99,
      'discountPercentage': 40.01,
      'currency': 'USD',
      'category': 'Sports & Outdoor',
      'description': 'Comfortable running shoes with air cushioning',
      'validFrom': DateTime.now(),
      'validTo': DateTime.now().add(const Duration(hours: 18)),
      'country': 'US',
      'city': 'Los Angeles',
      'isActive': true,
      'isFeatured': false,
      'viewCount': 856,
      'saveCount': 124,
      'shareCount': 45,
      'createdAt': DateTime.now().subtract(const Duration(hours: 6)),
      'updatedAt': DateTime.now(),
      'tags': ['shoes', 'nike', 'running', 'sports'],
      'sourceUrl': 'https://nike.com/air-max-270',
      'isFlashDeal': true,
    },
    {
      'id': 'promo_003',
      'storeId': 'store_apple',
      'storeName': 'Apple',
      'storeLogoUrl': AppConstants.storeLogos['Apple'] ?? '',
      'productName': 'MacBook Pro 14" M3',
      'productImageUrl': AppConstants.sampleImages[2],
      'originalPrice': 1999.00,
      'discountedPrice': 1699.00,
      'discountPercentage': 15.01,
      'currency': 'USD',
      'category': 'Electronics',
      'description': 'Professional laptop with M3 chip for creative work',
      'validFrom': DateTime.now().subtract(const Duration(hours: 2)),
      'validTo': DateTime.now().add(const Duration(days: 14)),
      'country': 'US',
      'city': 'San Francisco',
      'isActive': true,
      'isFeatured': true,
      'viewCount': 2341,
      'saveCount': 234,
      'shareCount': 78,
      'createdAt': DateTime.now().subtract(const Duration(hours: 2)),
      'updatedAt': DateTime.now(),
      'tags': ['laptop', 'apple', 'macbook', 'computer'],
      'sourceUrl': 'https://apple.com/macbook-pro',
      'isFlashDeal': false,
    },
    {
      'id': 'promo_004',
      'storeId': 'store_target',
      'storeName': 'Target',
      'storeLogoUrl': AppConstants.storeLogos['Target'] ?? '',
      'productName': 'Designer Handbag Collection',
      'productImageUrl': AppConstants.sampleImages[3],
      'originalPrice': 299.99,
      'discountedPrice': 179.99,
      'discountPercentage': 40.00,
      'currency': 'USD',
      'category': 'Fashion & Clothing',
      'description': 'Premium leather handbag with multiple compartments',
      'validFrom': DateTime.now().subtract(const Duration(days: 2)),
      'validTo': DateTime.now().add(const Duration(days: 5)),
      'country': 'US',
      'city': 'Chicago',
      'isActive': true,
      'isFeatured': false,
      'viewCount': 678,
      'saveCount': 92,
      'shareCount': 34,
      'createdAt': DateTime.now().subtract(const Duration(days: 1)),
      'updatedAt': DateTime.now(),
      'tags': ['handbag', 'fashion', 'leather', 'accessories'],
      'sourceUrl': 'https://target.com/handbag-collection',
      'isFlashDeal': false,
    },
    {
      'id': 'promo_005',
      'storeId': 'store_walmart',
      'storeName': 'Walmart',
      'storeLogoUrl': AppConstants.storeLogos['Walmart'] ?? '',
      'productName': 'Premium Coffee Machine',
      'productImageUrl': AppConstants.sampleImages[4],
      'originalPrice': 199.99,
      'discountedPrice': 129.99,
      'discountPercentage': 35.00,
      'currency': 'USD',
      'category': 'Home & Garden',
      'description': 'Automatic espresso and coffee machine with milk frother',
      'validFrom': DateTime.now(),
      'validTo': DateTime.now().add(const Duration(hours: 8)),
      'country': 'US',
      'city': 'Miami',
      'isActive': true,
      'isFeatured': false,
      'viewCount': 445,
      'saveCount': 67,
      'shareCount': 19,
      'createdAt': DateTime.now().subtract(const Duration(hours: 4)),
      'updatedAt': DateTime.now(),
      'tags': ['coffee', 'kitchen', 'appliance', 'espresso'],
      'sourceUrl': 'https://walmart.com/coffee-machine',
      'isFlashDeal': true,
    },
    {
      'id': 'promo_006',
      'storeId': 'store_bestbuy',
      'storeName': 'Best Buy',
      'storeLogoUrl': AppConstants.storeLogos['Best Buy'] ?? '',
      'productName': 'Wireless Noise-Canceling Headphones',
      'productImageUrl': AppConstants.sampleImages[5],
      'originalPrice': 349.99,
      'discountedPrice': 249.99,
      'discountPercentage': 28.57,
      'currency': 'USD',
      'category': 'Electronics',
      'description': 'Premium wireless headphones with active noise cancellation',
      'validFrom': DateTime.now().subtract(const Duration(hours: 8)),
      'validTo': DateTime.now().add(const Duration(days: 3)),
      'country': 'US',
      'city': 'Seattle',
      'isActive': true,
      'isFeatured': true,
      'viewCount': 1123,
      'saveCount': 156,
      'shareCount': 41,
      'createdAt': DateTime.now().subtract(const Duration(hours: 8)),
      'updatedAt': DateTime.now(),
      'tags': ['headphones', 'wireless', 'audio', 'noise-canceling'],
      'sourceUrl': 'https://bestbuy.com/wireless-headphones',
      'isFlashDeal': false,
    },
  ];
}