import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:dealbeacon/models/promotion.dart';
import 'package:dealbeacon/models/user_model.dart';
import 'package:dealbeacon/models/store.dart';
import 'package:dealbeacon/services/firestore_service.dart';

/// Repository pattern implementation for data operations
/// Provides a clean abstraction layer over FirestoreService
class DataRepository {
  final FirestoreService _firestoreService = FirestoreService();
  
  // Singleton pattern
  static final DataRepository _instance = DataRepository._internal();
  factory DataRepository() => _instance;
  DataRepository._internal();

  // User operations
  Future<Result<UserModel?>> getUser(String uid) async {
    try {
      final user = await _firestoreService.getUser(uid);
      return Result.success(user);
    } catch (e) {
      return Result.error('Failed to get user: ${e.toString()}');
    }
  }

  Future<Result<void>> createUser(UserModel user) async {
    try {
      await _firestoreService.createUser(user);
      return Result.success(null);
    } catch (e) {
      return Result.error('Failed to create user: ${e.toString()}');
    }
  }

  Future<Result<void>> updateUser(UserModel user) async {
    try {
      await _firestoreService.updateUser(user);
      return Result.success(null);
    } catch (e) {
      return Result.error('Failed to update user: ${e.toString()}');
    }
  }

  Future<Result<void>> deleteUser(String uid) async {
    try {
      await _firestoreService.deleteUser(uid);
      return Result.success(null);
    } catch (e) {
      return Result.error('Failed to delete user: ${e.toString()}');
    }
  }

  // Favorite operations with proper error handling
  Future<Result<void>> toggleFavoritePromotion(String uid, String promotionId) async {
    try {
      final user = await _firestoreService.getUser(uid);
      if (user == null) {
        return Result.error('User not found');
      }

      if (user.favoritePromotionIds.contains(promotionId)) {
        await _firestoreService.removeFavoritePromotion(uid, promotionId);
        await _firestoreService.incrementPromotionSave(promotionId);
      } else {
        await _firestoreService.addFavoritePromotion(uid, promotionId);
        await _firestoreService.incrementPromotionSave(promotionId);
      }
      
      return Result.success(null);
    } catch (e) {
      return Result.error('Failed to toggle favorite: ${e.toString()}');
    }
  }

  Future<Result<void>> toggleFavoriteStore(String uid, String storeId) async {
    try {
      final user = await _firestoreService.getUser(uid);
      if (user == null) {
        return Result.error('User not found');
      }

      if (user.favoriteStoreIds.contains(storeId)) {
        await _firestoreService.removeFavoriteStore(uid, storeId);
      } else {
        await _firestoreService.addFavoriteStore(uid, storeId);
      }
      
      return Result.success(null);
    } catch (e) {
      return Result.error('Failed to toggle favorite store: ${e.toString()}');
    }
  }

  // Promotion operations with error handling
  Stream<Result<List<Promotion>>> getPromotions({
    int limit = 20,
    String? category,
    String? country,
    String? storeId,
  }) {
    try {
      return _firestoreService
          .getPromotions(
            limit: limit,
            category: category,
            country: country,
            storeId: storeId,
          )
          .map((promotions) => Result.success(promotions))
          .handleError((error) => Result.error('Failed to load promotions: ${error.toString()}'));
    } catch (e) {
      return Stream.value(Result.error('Failed to initialize promotions stream: ${e.toString()}'));
    }
  }

  Stream<Result<List<Promotion>>> getFeaturedPromotions({int limit = 10}) {
    try {
      return _firestoreService
          .getFeaturedPromotions(limit: limit)
          .map((promotions) => Result.success(promotions))
          .handleError((error) => Result.error('Failed to load featured promotions: ${error.toString()}'));
    } catch (e) {
      return Stream.value(Result.error('Failed to initialize featured promotions stream: ${e.toString()}'));
    }
  }

  Stream<Result<List<Promotion>>> getFlashDeals({int limit = 10}) {
    try {
      return _firestoreService
          .getFlashDeals(limit: limit)
          .map((promotions) => Result.success(promotions))
          .handleError((error) => Result.error('Failed to load flash deals: ${error.toString()}'));
    } catch (e) {
      return Stream.value(Result.error('Failed to initialize flash deals stream: ${e.toString()}'));
    }
  }

  Future<Result<List<Promotion>>> searchPromotions(String searchTerm, {int limit = 20}) async {
    try {
      final promotions = await _firestoreService.searchPromotions(searchTerm, limit: limit);
      return Result.success(promotions);
    } catch (e) {
      return Result.error('Search failed: ${e.toString()}');
    }
  }

  Future<Result<Promotion?>> getPromotion(String id) async {
    try {
      final promotion = await _firestoreService.getPromotion(id);
      // Increment view count when getting promotion details
      if (promotion != null) {
        await _firestoreService.incrementPromotionView(id);
      }
      return Result.success(promotion);
    } catch (e) {
      return Result.error('Failed to get promotion: ${e.toString()}');
    }
  }

  Future<Result<void>> sharePromotion(String promotionId) async {
    try {
      await _firestoreService.incrementPromotionShare(promotionId);
      return Result.success(null);
    } catch (e) {
      return Result.error('Failed to record share: ${e.toString()}');
    }
  }

  // Store operations
  Stream<Result<List<Store>>> getStores({int limit = 20}) {
    try {
      return _firestoreService
          .getStores(limit: limit)
          .map((stores) => Result.success(stores))
          .handleError((error) => Result.error('Failed to load stores: ${error.toString()}'));
    } catch (e) {
      return Stream.value(Result.error('Failed to initialize stores stream: ${e.toString()}'));
    }
  }

  Future<Result<Store?>> getStore(String id) async {
    try {
      final store = await _firestoreService.getStore(id);
      return Result.success(store);
    } catch (e) {
      return Result.error('Failed to get store: ${e.toString()}');
    }
  }

  Stream<Result<List<Promotion>>> getStorePromotions(String storeId, {int limit = 10}) {
    try {
      return _firestoreService
          .getStorePromotions(storeId, limit: limit)
          .map((promotions) => Result.success(promotions))
          .handleError((error) => Result.error('Failed to load store promotions: ${error.toString()}'));
    } catch (e) {
      return Stream.value(Result.error('Failed to initialize store promotions stream: ${e.toString()}'));
    }
  }

  Stream<Result<List<Promotion>>> getFavoritePromotions(String uid) {
    try {
      return _firestoreService
          .getFavoritePromotions(uid)
          .map((promotions) => Result.success(promotions))
          .handleError((error) => Result.error('Failed to load favorites: ${error.toString()}'));
    } catch (e) {
      return Stream.value(Result.error('Failed to initialize favorites stream: ${e.toString()}'));
    }
  }

  // Categories
  List<String> get categories => _firestoreService.categories;

  // User submission operations
  Future<Result<void>> submitUserPromotion(Map<String, dynamic> submissionData) async {
    try {
      await _firestoreService.createUserSubmission(submissionData);
      return Result.success(null);
    } catch (e) {
      return Result.error('Failed to submit promotion: ${e.toString()}');
    }
  }

  // Analytics
  Future<Result<Map<String, int>>> getPromotionStats() async {
    try {
      final stats = await _firestoreService.getPromotionStats();
      return Result.success(stats);
    } catch (e) {
      return Result.error('Failed to get stats: ${e.toString()}');
    }
  }

  // Check connectivity and retry operations
  Future<bool> checkConnectivity() async {
    try {
      // Try to perform a simple read operation
      await FirebaseFirestore.instance
          .collection('promotions')
          .limit(1)
          .get(const GetOptions(source: Source.server));
      return true;
    } catch (e) {
      return false;
    }
  }
}

/// Result wrapper for better error handling
class Result<T> {
  final T? data;
  final String? error;
  final bool isSuccess;

  Result.success(this.data)
      : error = null,
        isSuccess = true;

  Result.error(this.error)
      : data = null,
        isSuccess = false;
}