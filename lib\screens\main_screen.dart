import 'package:flutter/material.dart';
import 'package:dealbeacon/screens/home/<USER>';
import 'package:dealbeacon/screens/search/search_screen.dart';
import 'package:dealbeacon/screens/favorites/favorites_screen.dart';
import 'package:dealbeacon/screens/profile/profile_screen.dart';
import 'package:dealbeacon/utils/constants.dart';

class MainScreen extends StatefulWidget {
  const MainScreen({super.key});

  @override
  State<MainScreen> createState() => _MainScreenState();
}

class _MainScreenState extends State<MainScreen> {
  int _currentIndex = 0;

  final List<Widget> _screens = const [
    HomeScreen(),
    SearchScreen(),
    FavoritesScreen(),
    ProfileScreen(),
  ];

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      body: IndexedStack(
        index: _currentIndex,
        children: _screens,
      ),
      bottomNavigationBar: NavigationBar(
        selectedIndex: _currentIndex,
        onDestinationSelected: (index) {
          setState(() => _currentIndex = index);
        },
        elevation: 0,
        backgroundColor: theme.colorScheme.surface,
        indicatorColor: theme.colorScheme.primary.withValues(alpha: 0.1),
        destinations: [
          NavigationDestination(
            icon: Icon(
              Icons.home_outlined,
              color: _currentIndex == 0 
                  ? theme.colorScheme.primary 
                  : theme.colorScheme.onSurface.withValues(alpha: 0.6),
            ),
            selectedIcon: Icon(
              Icons.home,
              color: theme.colorScheme.primary,
            ),
            label: 'Home',
          ),
          NavigationDestination(
            icon: Icon(
              Icons.search_outlined,
              color: _currentIndex == 1 
                  ? theme.colorScheme.primary 
                  : theme.colorScheme.onSurface.withValues(alpha: 0.6),
            ),
            selectedIcon: Icon(
              Icons.search,
              color: theme.colorScheme.primary,
            ),
            label: 'Search',
          ),
          NavigationDestination(
            icon: Icon(
              Icons.favorite_outline,
              color: _currentIndex == 2 
                  ? theme.colorScheme.primary 
                  : theme.colorScheme.onSurface.withValues(alpha: 0.6),
            ),
            selectedIcon: Icon(
              Icons.favorite,
              color: theme.colorScheme.primary,
            ),
            label: 'Favorites',
          ),
          NavigationDestination(
            icon: Icon(
              Icons.person_outline,
              color: _currentIndex == 3 
                  ? theme.colorScheme.primary 
                  : theme.colorScheme.onSurface.withValues(alpha: 0.6),
            ),
            selectedIcon: Icon(
              Icons.person,
              color: theme.colorScheme.primary,
            ),
            label: 'Profile',
          ),
        ],
      ),
    );
  }
}