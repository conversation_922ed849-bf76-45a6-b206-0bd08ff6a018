import 'package:flutter/material.dart';
import 'package:dealbeacon/widgets/promotion_card.dart';
import 'package:dealbeacon/widgets/category_chips.dart';
import 'package:dealbeacon/models/promotion.dart';
import 'package:dealbeacon/utils/constants.dart';
import 'package:dealbeacon/services/auth_service.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:intl/intl.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  String _selectedCategory = 'All';
  final Set<String> _favoritePromotions = {};

  // Convert sample data to Promotion objects for display
  List<Promotion> get _samplePromotions {
    return SampleData.promotions.map((data) {
      return Promotion(
        id: data['id'],
        storeId: data['storeId'],
        storeName: data['storeName'],
        storeLogoUrl: data['storeLogoUrl'],
        productName: data['productName'],
        productImageUrl: data['productImageUrl'],
        originalPrice: data['originalPrice'].toDouble(),
        discountedPrice: data['discountedPrice'].toDouble(),
        discountPercentage: data['discountPercentage'].toDouble(),
        currency: data['currency'],
        category: data['category'],
        description: data['description'],
        validFrom: data['validFrom'],
        validTo: data['validTo'],
        country: data['country'],
        city: data['city'],
        isActive: data['isActive'],
        isFeatured: data['isFeatured'],
        viewCount: data['viewCount'],
        saveCount: data['saveCount'],
        shareCount: data['shareCount'],
        createdAt: data['createdAt'],
        updatedAt: data['updatedAt'],
        tags: List<String>.from(data['tags']),
        sourceUrl: data['sourceUrl'],
        isFlashDeal: data['isFlashDeal'],
      );
    }).toList();
  }

  List<Promotion> get _filteredPromotions {
    final promotions = _samplePromotions;
    if (_selectedCategory == 'All') return promotions;
    return promotions.where((p) => p.category == _selectedCategory).toList();
  }

  List<Promotion> get _featuredPromotions {
    return _samplePromotions.where((p) => p.isFeatured).toList();
  }

  List<Promotion> get _flashDeals {
    return _samplePromotions.where((p) => p.isFlashDeal).toList();
  }

  void _toggleFavorite(String promotionId) {
    setState(() {
      if (_favoritePromotions.contains(promotionId)) {
        _favoritePromotions.remove(promotionId);
      } else {
        _favoritePromotions.add(promotionId);
      }
    });
    
    // TODO: Sync with Firestore when available
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final userName = AuthService.currentUserDisplayName ?? 'User';

    return Scaffold(
      backgroundColor: theme.colorScheme.surface,
      body: CustomScrollView(
        slivers: [
          // App Bar
          SliverAppBar(
            floating: true,
            backgroundColor: theme.colorScheme.surface,
            elevation: 0,
            title: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Hello, $userName! 👋',
                  style: theme.textTheme.headlineSmall?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: theme.colorScheme.onSurface,
                  ),
                ),
                Text(
                  'Discover amazing deals today',
                  style: theme.textTheme.bodyMedium?.copyWith(
                    color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
                  ),
                ),
              ],
            ),
            actions: [
              Container(
                margin: const EdgeInsets.only(right: AppConstants.paddingMedium),
                decoration: BoxDecoration(
                  color: theme.colorScheme.primaryContainer,
                  shape: BoxShape.circle,
                ),
                child: IconButton(
                  onPressed: () {
                    // TODO: Implement notifications screen
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(content: Text('Notifications coming soon!')),
                    );
                  },
                  icon: Icon(
                    Icons.notifications_outlined,
                    color: theme.colorScheme.primary,
                  ),
                ),
              ),
            ],
          ),

          // Categories
          SliverToBoxAdapter(
            child: Container(
              padding: const EdgeInsets.symmetric(vertical: AppConstants.paddingMedium),
              child: CategoryChips(
                categories: ['All'] + AppConstants.categoryIcons.keys.toList(),
                selectedCategory: _selectedCategory,
                onCategorySelected: (category) {
                  setState(() => _selectedCategory = category);
                },
              ),
            ),
          ),

          // Flash Deals Section
          if (_flashDeals.isNotEmpty) ...[
            SliverToBoxAdapter(
              child: Padding(
                padding: const EdgeInsets.all(AppConstants.paddingMedium),
                child: Row(
                  children: [
                    Icon(
                      Icons.flash_on,
                      color: theme.colorScheme.error,
                      size: 24,
                    ),
                    const SizedBox(width: AppConstants.paddingSmall),
                    Text(
                      'Flash Deals',
                      style: theme.textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: theme.colorScheme.error,
                      ),
                    ),
                    const SizedBox(width: AppConstants.paddingSmall),
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: AppConstants.paddingSmall,
                        vertical: 2,
                      ),
                      decoration: BoxDecoration(
                        color: theme.colorScheme.error,
                        borderRadius: BorderRadius.circular(AppConstants.radiusSmall),
                      ),
                      child: Text(
                        'LIMITED TIME',
                        style: theme.textTheme.labelSmall?.copyWith(
                          color: theme.colorScheme.onError,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
            SliverToBoxAdapter(
              child: SizedBox(
                height: 420,
                child: ListView.builder(
                  scrollDirection: Axis.horizontal,
                  padding: const EdgeInsets.symmetric(horizontal: AppConstants.paddingMedium),
                  itemCount: _flashDeals.length,
                  itemBuilder: (context, index) {
                    final promotion = _flashDeals[index];
                    return SizedBox(
                      width: 320,
                      child: PromotionCard(
                        promotion: promotion,
                        isFavorited: _favoritePromotions.contains(promotion.id),
                        onFavorite: () => _toggleFavorite(promotion.id),
                        onTap: () {
                          // TODO: Navigate to promotion details
                          ScaffoldMessenger.of(context).showSnackBar(
                            SnackBar(content: Text('Opening ${promotion.productName}')),
                          );
                        },
                      ),
                    );
                  },
                ),
              ),
            ),
          ],

          // Featured Section
          if (_featuredPromotions.isNotEmpty && _selectedCategory == 'All') ...[
            SliverToBoxAdapter(
              child: Padding(
                padding: const EdgeInsets.all(AppConstants.paddingMedium),
                child: Row(
                  children: [
                    Icon(
                      Icons.star,
                      color: theme.colorScheme.primary,
                      size: 24,
                    ),
                    const SizedBox(width: AppConstants.paddingSmall),
                    Text(
                      'Featured Deals',
                      style: theme.textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
              ),
            ),
            SliverToBoxAdapter(
              child: SizedBox(
                height: 420,
                child: ListView.builder(
                  scrollDirection: Axis.horizontal,
                  padding: const EdgeInsets.symmetric(horizontal: AppConstants.paddingMedium),
                  itemCount: _featuredPromotions.length,
                  itemBuilder: (context, index) {
                    final promotion = _featuredPromotions[index];
                    return SizedBox(
                      width: 320,
                      child: PromotionCard(
                        promotion: promotion,
                        isFavorited: _favoritePromotions.contains(promotion.id),
                        onFavorite: () => _toggleFavorite(promotion.id),
                        onTap: () {
                          // TODO: Navigate to promotion details
                          ScaffoldMessenger.of(context).showSnackBar(
                            SnackBar(content: Text('Opening ${promotion.productName}')),
                          );
                        },
                      ),
                    );
                  },
                ),
              ),
            ),
          ],

          // All Promotions Section
          SliverToBoxAdapter(
            child: Padding(
              padding: const EdgeInsets.all(AppConstants.paddingMedium),
              child: Row(
                children: [
                  Text(
                    _selectedCategory == 'All' ? 'All Deals' : _selectedCategory,
                    style: theme.textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const Spacer(),
                  TextButton(
                    onPressed: () {
                      // TODO: Navigate to see all promotions
                    },
                    child: Text(
                      'See All',
                      style: TextStyle(
                        color: theme.colorScheme.primary,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),

          // Promotions List
          if (_filteredPromotions.isEmpty)
            SliverToBoxAdapter(
              child: Container(
                height: 200,
                alignment: Alignment.center,
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.search_off,
                      size: 64,
                      color: theme.colorScheme.onSurface.withValues(alpha: 0.3),
                    ),
                    const SizedBox(height: AppConstants.paddingMedium),
                    Text(
                      'No promotions found',
                      style: theme.textTheme.titleMedium?.copyWith(
                        color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
                      ),
                    ),
                    const SizedBox(height: AppConstants.paddingSmall),
                    Text(
                      'Try selecting a different category',
                      style: theme.textTheme.bodyMedium?.copyWith(
                        color: theme.colorScheme.onSurface.withValues(alpha: 0.5),
                      ),
                    ),
                  ],
                ),
              ),
            )
          else
            SliverList(
              delegate: SliverChildBuilderDelegate(
                (context, index) {
                  final promotion = _filteredPromotions[index];
                  return PromotionCard(
                    promotion: promotion,
                    isFavorited: _favoritePromotions.contains(promotion.id),
                    onFavorite: () => _toggleFavorite(promotion.id),
                    onTap: () {
                      // TODO: Navigate to promotion details
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(content: Text('Opening ${promotion.productName}')),
                      );
                    },
                  );
                },
                childCount: _filteredPromotions.length,
              ),
            ),

          // Bottom Padding
          const SliverToBoxAdapter(
            child: SizedBox(height: AppConstants.paddingXLarge),
          ),
        ],
      ),
    );
  }
}