# PromoDetect MVP Architecture

## Overview
Building a cross-platform mobile application for collecting and displaying product promotions with Firebase integration. This MVP focuses on core user features with a foundation for future expansion.

## Core MVP Features
1. **User Authentication**: Firebase Auth with email/Google sign-in
2. **Promotion Display**: Browse promotions by category, store, and location
3. **Search & Filter**: Search promotions and filter by discount percentage, date
4. **Favorites**: Save favorite promotions locally with sync capability
5. **User Submissions**: Upload promotion images for community review
6. **Push Notifications**: Basic notification system for new promotions

## Technical Stack
- **Frontend**: Flutter with Material Design 3
- **Backend**: Firebase (Firestore, Auth, Cloud Functions, FCM)
- **Local Storage**: SharedPreferences, Hive
- **State Management**: Provider/Riverpod
- **Image Handling**: Image picker, caching

## App Structure

### Data Models
1. **Promotion**: store, product, originalPrice, discountPrice, discount%, category, validFrom, validTo, country, imageUrl
2. **User**: uid, email, displayName, favoriteStoreIds, notificationPreferences
3. **Store**: id, name, logoUrl, category, country
4. **UserSubmission**: promotionData, imageUrl, status, submittedBy, submittedAt

### Core Screens
1. **Authentication Screens**: Login, Register, Password Reset
2. **Main Navigation**: Bottom navigation with Home, Search, Favorites, Profile
3. **Home Screen**: Featured promotions, categories, nearby stores
4. **Search Screen**: Search bar with filters and results
5. **Promotion Details**: Full promotion info with share options
6. **Profile Screen**: User settings, submission history, logout
7. **Submission Screen**: Camera/gallery picker with form

### Key Components
1. **PromotionCard**: Reusable promotion display widget
2. **FilterBottomSheet**: Advanced filtering options
3. **CategoryChips**: Horizontal category selector
4. **CountdownTimer**: Time remaining for flash deals
5. **ImageUploader**: Handles image selection and upload

## Implementation Steps

### Phase 1: Foundation
1. Set up Firebase project and configure Flutter app
2. Implement authentication screens and flow
3. Create data models and Firebase services
4. Build basic UI theme and navigation

### Phase 2: Core Features  
1. Implement promotion listing with sample data
2. Add search and filtering functionality
3. Create promotion detail screen
4. Build user favorites system

### Phase 3: User Engagement
1. Implement image upload for user submissions
2. Add push notification setup
3. Create profile management
4. Add sharing functionality

### Phase 4: Enhancement & Testing
1. Add offline caching for promotions
2. Implement location-based filtering
3. Add analytics tracking
4. Comprehensive testing and bug fixes

## File Structure
```
lib/
├── main.dart
├── theme.dart
├── models/
│   ├── promotion.dart
│   ├── user_model.dart
│   └── store.dart
├── services/
│   ├── auth_service.dart
│   ├── firestore_service.dart
│   └── notification_service.dart
├── screens/
│   ├── auth/
│   ├── home/
│   ├── search/
│   ├── favorites/
│   └── profile/
├── widgets/
│   ├── promotion_card.dart
│   ├── category_chips.dart
│   └── filter_bottom_sheet.dart
└── utils/
    ├── constants.dart
    └── helpers.dart
```

## Sample Data Categories
- Electronics
- Fashion & Clothing  
- Grocery & Food
- Home & Garden
- Health & Beauty
- Sports & Outdoor
- Books & Media
- Travel & Hotels

This MVP provides a solid foundation that can be extended with advanced features like web scraping, AI-powered text extraction, merchant dashboards, and comprehensive analytics in future iterations.